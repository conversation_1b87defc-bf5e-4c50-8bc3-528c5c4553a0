#!/usr/bin/env python3
"""
MusicGen依赖安装脚本
自动安装MusicGen所需的依赖库
"""

import subprocess
import sys
import os

def run_command(command):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def check_gpu():
    """检查GPU支持"""
    print("检查GPU支持...")
    
    try:
        import torch
        if torch.cuda.is_available():
            print(f"✓ 检测到CUDA GPU: {torch.cuda.get_device_name()}")
            return True
        else:
            print("! 未检测到CUDA GPU，将使用CPU模式")
            return False
    except ImportError:
        print("! PyTorch未安装，无法检测GPU")
        return False

def install_pytorch():
    """安装PyTorch"""
    print("\n安装PyTorch...")
    
    # 检查是否有GPU
    has_gpu = False
    try:
        success, stdout, stderr = run_command("nvidia-smi")
        if success:
            has_gpu = True
            print("✓ 检测到NVIDIA GPU")
        else:
            print("! 未检测到NVIDIA GPU，安装CPU版本")
    except:
        print("! 无法检测GPU，安装CPU版本")
    
    if has_gpu:
        # 安装GPU版本
        command = "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118"
    else:
        # 安装CPU版本
        command = "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu"
    
    print(f"执行命令: {command}")
    success, stdout, stderr = run_command(command)
    
    if success:
        print("✓ PyTorch安装成功")
        return True
    else:
        print(f"✗ PyTorch安装失败: {stderr}")
        return False

def install_transformers():
    """安装Transformers"""
    print("\n安装Transformers...")
    
    print(f"执行命令: {command}")
    success, stdout, stderr = run_command(command)
    
    if success:
        print("✓ Transformers安装成功")
        return True
    else:
        print(f"✗ Transformers安装失败: {stderr}")
        return False

def install_audio_deps():
    """安装音频处理依赖"""
    print("\n安装音频处理依赖...")
    
    deps = [
        "scipy",
        "numpy",
        "soundfile",
        "librosa"
    ]
    
    for dep in deps:
        command = f"pip install {dep}"
        print(f"安装 {dep}...")
        success, stdout, stderr = run_command(command)
        
        if success:
            print(f"✓ {dep} 安装成功")
        else:
            print(f"✗ {dep} 安装失败: {stderr}")
            return False
    
    return True

def verify_installation():
    """验证安装"""
    print("\n验证安装...")
    
    try:
        import torch
        print(f"✓ PyTorch {torch.__version__}")
        print(f"✓ CUDA可用: {torch.cuda.is_available()}")
    except ImportError:
        print("✗ PyTorch导入失败")
        return False
    
    try:
        import transformers
        print(f"✓ Transformers {transformers.__version__}")
    except ImportError:
        print("✗ Transformers导入失败")
        return False
    
    try:
        import scipy
        print(f"✓ SciPy {scipy.__version__}")
    except ImportError:
        print("✗ SciPy导入失败")
        return False
    
    try:
        import numpy as np
        print(f"✓ NumPy {np.__version__}")
    except ImportError:
        print("✗ NumPy导入失败")
        return False
    
    try:
        import soundfile
        print(f"✓ SoundFile可用")
    except ImportError:
        print("✗ SoundFile导入失败")
        return False
    
    # 测试MusicGen模型导入
    try:
        from transformers import MusicgenForConditionalGeneration, AutoProcessor
        print("✓ MusicGen模型类导入成功")
    except ImportError as e:
        print(f"✗ MusicGen模型类导入失败: {e}")
        return False
    
    return True

def download_test_model():
    """下载测试模型"""
    print("\n下载测试模型...")
    
    try:
        from transformers import MusicgenForConditionalGeneration, AutoProcessor
        
        model_name = "facebook/musicgen-medium"
        print(f"下载模型: {model_name}")
        
        # 下载处理器
        print("下载处理器...")
        processor = AutoProcessor.from_pretrained(model_name)
        print("✓ 处理器下载成功")
        
        # 下载模型（这可能需要一些时间）
        print("下载模型（这可能需要几分钟）...")
        model = MusicgenForConditionalGeneration.from_pretrained(model_name)
        print("✓ 模型下载成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 模型下载失败: {e}")
        return False

def main():
    """主安装函数"""
    print("MusicGen依赖安装脚本")
    print("=" * 50)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("✗ 需要Python 3.8或更高版本")
        return
    
    print(f"✓ Python版本: {sys.version}")
    
    # 升级pip
    print("\n升级pip...")
    run_command("pip install --upgrade pip")
    
    # 安装PyTorch
    if not install_pytorch():
        print("PyTorch安装失败，退出")
        return
    
    # 安装Transformers
    if not install_transformers():
        print("Transformers安装失败，退出")
        return
    
    # 安装音频处理依赖
    if not install_audio_deps():
        print("音频处理依赖安装失败，退出")
        return
    
    # 验证安装
    if not verify_installation():
        print("安装验证失败")
        return
    
    # 询问是否下载测试模型
    response = input("\n是否下载测试模型 facebook/musicgen-medium？(y/n): ")
    if response.lower() in ['y', 'yes']:
        download_test_model()
    
    print("\n" + "=" * 50)
    print("✓ MusicGen依赖安装完成！")
    print("\n现在可以运行测试脚本:")
    print("python test_musicgen.py")

if __name__ == "__main__":
    main()
