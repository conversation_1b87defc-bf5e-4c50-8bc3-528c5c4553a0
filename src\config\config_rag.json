{"embedding": {"model": "Qwen/Qwen3-Embedding-0.6B", "dimension": 1024, "batch_size": 32, "use_local": true, "api_key": "", "base_url": "", "timeout": 60}, "vector_store": {"use_faiss": true, "index_type": "IVF", "nlist": 100, "similarity_metric": "cosine", "index_directory": "vector_indices"}, "retrieval": {"similarity_threshold": 0.7, "max_results": 10, "default_top_k": 5, "enable_rerank": false, "rerank_model": "", "enable_filter": true}, "sad_processing": {"chunk_size": 1000, "chunk_overlap": 100, "enable_summary": true, "summary_max_length": 300, "summary_min_length": 50, "output_directory": "sad_files"}, "vsi_processing": {"supported_formats": [".jpg", ".jpeg", ".png", ".bmp", ".gif"], "enable_ocr": true, "ocr_language": "chi_sim", "enable_image_description": true, "description_model": "blip2", "output_directory": "vsi_files"}, "indexing": {"auto_rebuild": false, "incremental_update": true, "backup_indices": true, "max_index_size_gb": 10, "cleanup_old_indices": true}, "performance": {"max_workers": 4, "memory_limit_gb": 8, "cache_size": 1000, "enable_parallel_processing": true}}