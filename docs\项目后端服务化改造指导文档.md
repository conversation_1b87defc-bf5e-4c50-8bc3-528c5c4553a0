# 项目后端服务化改造指导文档

## 🎯 改造目标

- 将 `launcher.py` 中的 CLI 主程序拆分为可由 GUI 调用的后端模块。
- 引入轻量级服务接口（不强制遵循 RESTful 规范）。
- 保持函数式调用风格，避免大量 HTTP 状态语义设计。
- 提供统一的“服务注册与调用入口”，支持 GUI 通过调用指定方法完成启动器功能。

------

## 🗂️ 改造后项目结构建议

```
your_project_root/
├── backend/                  # ✅ 启动器后端服务（GUI 接口、中间层）
│   ├── main.py               # 服务入口（如 FastAPI、Flask）
│   ├── router.py             # API 路由（统一调度）
│   ├── service_registry.py   # 注册各个功能的封装
│   └── requirements.txt      # 后端依赖
│
├── ai_workflow/              # ✅ 原有的控制器、生成器、业务逻辑（独立模块化）
│   ├── controller/
│   ├── generators/
│   ├── preprocess/
│   ├── rag/
│   ├── prompts/
│   ├── tests/
│   └── utils/
│
├── cli_launcher/             # ✅ 保留原有 CLI 启动器（命令行交互）
│   └── launcher.py
│
├── gui_launcher/             # ✅ 单独的 GUI 项目（推荐 Electron / PyQt / WPF）
│   ├── [frontend code...]
│   ├── package.json
│   └── ...
│
├── config/
│   └── config.ini
│
├── README.md
└── pyproject.toml / setup.py # 管理 ai_workflow 模块
```

------

## 🔧 改造步骤详解

------

### 第一步：提取 CLI 逻辑为函数接口

**目的**：把 CLI 中的 `input()`、`print()` 交互去掉，变成 GUI 可调用的函数

#### ✔ 示例 - 原 `launcher.py`

```
def start():
    theme = input("请输入主题: ")
    material_paths = input("请输入素材路径（以逗号分隔）: ").split(",")

    controller = MainPipelineController()
    result = controller.generate_complete_game(theme=theme, material_paths=material_paths)
    print("生成完成:", result)
```

#### ✅ 改造为纯函数

```
# src/backend/service_registry.py
def generate_complete_game_service(theme: str, material_paths: list[str]) -> dict:
    controller = MainPipelineController()
    result = controller.generate_complete_game(
        theme=theme,
        material_paths=material_paths
    )
    return result
```

------

### 第二步：建立统一服务注册表

> 利用类似“RPC注册”的思路，GUI 只需要调用服务名和参数即可，避免固定 API 路由设计。

```
# src/backend/service_registry.py

from typing import Callable

_services: dict[str, Callable] = {}

def register_service(name: str):
    def decorator(func: Callable):
        _services[name] = func
        return func
    return decorator

def call_service(name: str, **kwargs):
    if name not in _services:
        raise ValueError(f"服务 {name} 未注册")
    return _services[name](**kwargs)

# 注册服务
@register_service("generate_complete_game")
def generate_complete_game_service(theme: str, material_paths: list[str]):
    from controller.main_pipeline_controller import MainPipelineController
    controller = MainPipelineController()
    return controller.generate_complete_game(theme=theme, material_paths=material_paths)
```

------

### 第三步：构建服务入口（router）

```
# src/backend/router.py

from fastapi import APIRouter, Request
from pydantic import BaseModel
from backend.service_registry import call_service

router = APIRouter()

class GenericCallRequest(BaseModel):
    service: str
    params: dict

@router.post("/call")
async def call(request: GenericCallRequest):
    try:
        result = call_service(request.service, **request.params)
        return {"status": "success", "result": result}
    except Exception as e:
        return {"status": "error", "message": str(e)}
```

------

### 第四步：启动服务（server）

```
# src/backend/server.py

from fastapi import FastAPI
from backend.router import router
import uvicorn

app = FastAPI()
app.include_router(router)

if __name__ == "__main__":
    uvicorn.run("backend.server:app", host="127.0.0.1", port=8080, reload=True)
```

------

### 第五步：GUI 调用服务

#### 🎯 GUI 调用统一 POST 接口 `/call`

```
POST http://127.0.0.1:8080/call
{
  "service": "generate_complete_game",
  "params": {
    "theme": "雷州文化",
    "material_paths": ["materials/雷州文化.txt"]
  }
}
```

------

## ✅ 优点

| 特性             | 说明                                          |
| ---------------- | --------------------------------------------- |
| 保留函数式逻辑   | 各模块保持现有结构                            |
| 松耦合           | GUI 只需传入服务名和参数，不依赖具体代码实现  |
| 无需全套 RESTful | 避免 GET/PUT/DELETE 等繁琐语义                |
| 易扩展           | 新服务只需 `@register_service` 装饰器即可添加 |



------

## 🧪 建议测试策略

1. 启动 `server.py`

2. 用 Postman 或 curl 测试 `/call` 接口：

   ```
   curl -X POST http://localhost:8080/call -H "Content-Type: application/json" -d '{
     "service": "generate_complete_game",
     "params": {
       "theme": "中国风",
       "material_paths": ["data/test.txt"]
     }
   }'
   ```

3. GUI 可通过 Python `requests` 或 .NET `HttpClient` 调用此接口

------

## 🔚 后续方向

- 实现异步任务：使用 `background_tasks` 或 `celery` 管理进度
- 增加日志订阅接口：前端可查看实时日志
- 增加“功能发现”接口：列出所有已注册服务
- 使用 `.env` 管理配置

