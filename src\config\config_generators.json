{"image_generation": {"similarity_threshold": 0.2, "ip_adapter_scale": 0.5, "auto_build_index": true, "default_steps": 25, "default_guidance_scale": 7.5, "dimensions": {"background": {"width": 960, "height": 540}, "character": {"width": 512, "height": 768}}, "prompt_templates": {"background": "masterpiece, wallpaper, 8k, detailed CG, {prompt}, (no_human)", "character": "masterpiece, wallpaper, 8k, detailed CG, {prompt}, (upper_body), solo"}}, "vocal_generation": {"default_voice": "001", "audio_format": "wav", "sample_rate": 22050, "character_voice_mapping": {"narrator": "006", "林小满": "002", "祖母": "005", "村民": "003", "少年": "004", "女性角色": "001", "男性角色": "003"}, "voice_id_mapping": {"1": "001", "2": "002", "3": "003", "4": "004", "5": "005", "6": "006"}}, "music_generation": {"model_name": "facebook/musicgen-medium", "default_duration": 30, "sample_rate": 32000, "audio_format": "wav", "style_prompts": {"sad": "sad, melancholy, piano, emotional", "common": "peaceful, background, ambient", "epic": "epic, orchestral, dramatic", "ambient": "ambient, atmospheric, calm", "upbeat": "upbeat, energetic, cheerful"}}, "narrative_generation": {"model": "deepseek-reasoner", "temperature": 0.7, "max_tokens": 2000, "timeout": 120, "enable_rag": true, "rag_top_k": 3, "max_context_items": 3, "max_summary_length": 200, "max_description_items": 2, "max_theme_items": 3, "max_theme_length": 150}, "framework_generation": {"model": "deepseek-reasoner", "temperature": 0.8, "max_tokens": 6000, "timeout": 180, "enable_rag": true, "rag_top_k": 5, "max_nodes": 10, "default_character": {"name": "主角", "description": "一位对文化遗产感兴趣的年轻人", "details": ["平凡的背景", "好奇心强", "容易代入"]}, "default_node": {"description_template": "主角开始探索{theme}的旅程", "key_events": ["初次接触主题", "建立基本设定"], "characters": ["主角"]}}, "renpy_integration": {"output_directory": "renpy_game", "script_filename": "script.rpy", "enable_gui_generation": true, "enable_options_generation": true, "character_prefix": "define", "label_prefix": "label"}, "ui_design": {"output_directory": "ui", "theme": "cultural_heritage", "enable_responsive": true, "color_scheme": "traditional", "font_family": "default"}}