# 遗游记 后端架构图表

## 🏗️ 系统整体架构

```mermaid
graph TB
    subgraph "Frontend Layer"
        GUI[GUI Application<br/>Electron/PyQt/WPF]
    end
    
    subgraph "Backend API Layer"
        API[FastAPI Server<br/>server.py]
        Router[API Router<br/>router.py]
    end
    
    subgraph "Service Layer"
        Registry[Service Registry<br/>service_registry.py]
        Services[Game Services<br/>game_services.py]
    end
    
    subgraph "Core Business Layer"
        Controller[Main Pipeline Controller]
        Generators[Content Generators]
        Preprocess[Material Preprocessor]
        RAG[RAG Retrieval System]
        Utils[Utility Functions]
    end
    
    GUI -->|HTTP API| API
    API --> Router
    Router --> Registry
    Registry --> Services
    Services --> Controller
    Services --> Generators
    Services --> Preprocess
    Services --> RAG
    Services --> Utils
```

## 🔄 服务调用流程

```mermaid
sequenceDiagram
    participant F as Frontend
    participant R as Router
    participant S as Service Registry
    participant G as Game Services
    participant C as Core Logic
    
    Note over F,C: 同步调用流程
    F->>R: POST /api/v1/call
    R->>S: call_service(name, params)
    S->>G: execute service function
    G->>C: call core business logic
    C-->>G: return result
    G-->>S: return formatted response
    S-->>R: return response
    R-->>F: HTTP response
    
    Note over F,C: 异步调用流程
    F->>R: POST /api/v1/call-async
    R->>R: create task_id
    R-->>F: return task_id
    R->>G: background task execution
    G->>C: call core business logic
    F->>R: GET /api/v1/task/{id}
    R-->>F: task status
```

## 📦 组件依赖关系

```mermaid
graph LR
    subgraph "API Layer"
        Server[server.py]
        Router[router.py]
    end
    
    subgraph "Service Layer"
        Registry[service_registry.py]
        GameServices[game_services.py]
    end
    
    subgraph "Core Layer"
        Controller[controller/]
        Generators[generators/]
        Preprocess[preprocess/]
        RAG[rag/]
        Utils[utils/]
    end
    
    Server --> Router
    Router --> Registry
    Registry --> GameServices
    GameServices --> Controller
    GameServices --> Generators
    GameServices --> Preprocess
    GameServices --> RAG
    GameServices --> Utils
    
    Controller --> Generators
    Controller --> Preprocess
    Controller --> RAG
    Controller --> Utils
```

## 🎯 服务注册机制

```mermaid
graph TD
    A[Service Function] -->|@register_service| B[Decorator]
    B --> C[Extract Function Signature]
    B --> D[Extract Parameters Info]
    B --> E[Store in Global Registry]
    
    F[API Call] --> G[call_service()]
    G --> H[Lookup Service in Registry]
    H --> I[Validate Parameters]
    I --> J[Execute Service Function]
    J --> K[Return Formatted Response]
    
    E -.-> H
```

## 🔧 数据流向图

```mermaid
flowchart TD
    A[Frontend Request] --> B{Request Type}
    
    B -->|Sync| C[/api/v1/call]
    B -->|Async| D[/api/v1/call-async]
    
    C --> E[Service Registry]
    D --> F[Create Task]
    F --> G[Background Execution]
    G --> E
    
    E --> H[Game Services]
    H --> I{Service Type}
    
    I -->|Game Generation| J[generate_complete_game]
    I -->|Material Processing| K[test_material_preprocessing]
    I -->|RAG Retrieval| L[test_rag_retrieval]
    I -->|System Test| M[run_system_test]
    
    J --> N[Main Pipeline Controller]
    K --> O[Material Preprocessor]
    L --> P[RAG Retrieval]
    M --> Q[System Tester]
    
    N --> R[Response]
    O --> R
    P --> R
    Q --> R
    
    R --> S[Formatted JSON Response]
    S --> T[Frontend]
```

## 🏃‍♂️ 异步任务管理

```mermaid
stateDiagram-v2
    [*] --> pending: Create Task
    pending --> running: Start Execution
    running --> completed: Success
    running --> failed: Error
    running --> cancelled: User Cancel
    
    completed --> [*]
    failed --> [*]
    cancelled --> [*]
    
    note right of pending
        Task created with UUID
        Stored in memory
    end note
    
    note right of running
        Background execution
        Status can be queried
    end note
    
    note right of completed
        Result available
        Can be retrieved
    end note
```

## 🔍 服务发现流程

```mermaid
graph LR
    A[Service Registration] --> B[Global Registry]
    B --> C[Service Metadata]
    
    D[Frontend Request] --> E[GET /api/v1/services]
    E --> F[Query Registry]
    F --> G[Return Service List]
    
    H[Service Info Request] --> I[GET /api/v1/services/{name}]
    I --> J[Query Specific Service]
    J --> K[Return Service Details]
    
    B -.-> F
    B -.-> J
```

## 📊 错误处理机制

```mermaid
flowchart TD
    A[API Request] --> B[Router Validation]
    B -->|Valid| C[Service Registry]
    B -->|Invalid| D[HTTP 400 Error]
    
    C --> E[Service Execution]
    E -->|Success| F[Success Response]
    E -->|Business Error| G[Error Response]
    E -->|System Error| H[Exception Handler]
    
    H --> I[Global Exception Handler]
    I --> J[HTTP 500 Error]
    
    F --> K[JSON Response]
    G --> K
    D --> K
    J --> K
    
    K --> L[Frontend]
```

## 🚀 扩展点设计

```mermaid
graph TB
    subgraph "Extension Points"
        A[New Service Registration]
        B[New Generator Addition]
        C[New Middleware]
        D[New Validator]
    end
    
    A --> E[service_registry.py]
    B --> F[generators/]
    C --> G[server.py]
    D --> H[router.py]
    
    E --> I[Automatic Discovery]
    F --> J[Pipeline Integration]
    G --> K[Request Processing]
    H --> L[Parameter Validation]
```

这些图表展示了 遗游记 后端的完整架构设计，包括：

1. **系统整体架构** - 展示各层之间的关系
2. **服务调用流程** - 同步和异步调用的时序图
3. **组件依赖关系** - 模块间的依赖关系
4. **服务注册机制** - 服务自动注册的流程
5. **数据流向图** - 请求处理的完整流程
6. **异步任务管理** - 任务状态转换图
7. **服务发现流程** - 服务发现机制
8. **错误处理机制** - 异常处理流程
9. **扩展点设计** - 系统扩展点

通过这些图表，您可以清楚地理解后端架构的设计思路和实现方式。
