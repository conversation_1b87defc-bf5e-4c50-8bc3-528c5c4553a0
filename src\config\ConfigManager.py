import json
import os
from pathlib import Path
from typing import Any, Dict, Optional, Union

class ConfigManager:
    """统一配置管理器"""

    def __init__(self, config_dir: str):
        self.config_dir = Path(config_dir)
        self._configs: Dict[str, Dict[str, Any]] = {}

        # 确保配置目录存在
        self.config_dir.mkdir(parents=True, exist_ok=True)

    def load(self, name: str) -> Dict[str, Any]:
        """加载一个配置文件"""
        if name not in self._configs:
            file_path = self.config_dir / f"config_{name}.json"
            if not file_path.exists():
                raise FileNotFoundError(f"配置文件不存在: {file_path}")
            with open(file_path, "r", encoding="utf-8") as f:
                self._configs[name] = json.load(f)
        return self._configs[name]

    def save(self, name: str) -> None:
        """保存某个配置到文件"""
        if name in self._configs:
            file_path = self.config_dir / f"config_{name}.json"
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(self._configs[name], f, indent=2, ensure_ascii=False)

    def get(self, name: str, key: str, default=None):
        """获取配置值，支持嵌套键"""
        config = self.load(name)

        # 支持嵌套键，如 "image_generation.use_cloud"
        if '.' in key:
            keys = key.split('.')
            value = config
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
            return value
        else:
            return config.get(key, default)

    def set(self, name: str, key: str, value: Any) -> None:
        """设置配置值并保存，支持嵌套键"""
        config = self.load(name)

        # 支持嵌套键设置
        if '.' in key:
            keys = key.split('.')
            current = config
            for k in keys[:-1]:
                if k not in current:
                    current[k] = {}
                current = current[k]
            current[keys[-1]] = value
        else:
            config[key] = value

        self.save(name)

    def reload(self, name: str) -> None:
        """重新加载配置"""
        if name in self._configs:
            del self._configs[name]
        self.load(name)

    def get_section(self, name: str, section: str) -> Dict[str, Any]:
        """获取配置文件中的某个节"""
        config = self.load(name)
        return config.get(section, {})

    def list_configs(self) -> list:
        """列出所有可用的配置文件"""
        config_files = []
        for file_path in self.config_dir.glob("config_*.json"):
            config_name = file_path.stem.replace("config_", "")
            config_files.append(config_name)
        return config_files

    def validate_config(self, name: str) -> bool:
        """验证配置文件是否有效"""
        try:
            self.load(name)
            return True
        except (FileNotFoundError, json.JSONDecodeError):
            return False


# 全局配置管理器实例
_config_manager: Optional[ConfigManager] = None

def get_config_manager() -> ConfigManager:
    """获取全局配置管理器实例"""
    global _config_manager
    if _config_manager is None:
        # 获取配置目录路径
        current_dir = Path(__file__).parent
        _config_manager = ConfigManager(str(current_dir))
    return _config_manager

def get_config(module: str, key: str, default=None):
    """便捷函数：获取配置值"""
    return get_config_manager().get(module, key, default)

def set_config(module: str, key: str, value: Any) -> None:
    """便捷函数：设置配置值"""
    get_config_manager().set(module, key, value)

def get_section(module: str, section: str) -> Dict[str, Any]:
    """便捷函数：获取配置节"""
    return get_config_manager().get_section(module, section)
