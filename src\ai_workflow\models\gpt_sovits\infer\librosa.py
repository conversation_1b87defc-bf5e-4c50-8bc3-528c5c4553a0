import soundfile as sf
import numpy as np
from scipy.signal import resample_poly

# 代替 librosa.load
def load_audio(path, sr=None):
    audio, orig_sr = sf.read(path, dtype='float32')
    if audio.ndim > 1:
        audio = np.mean(audio, axis=1)  # 转单声道
    if sr is not None and sr != orig_sr:
        audio = resample_audio(audio, orig_sr, sr)
        orig_sr = sr
    return audio, orig_sr

# 代替 librosa.resample
def resample_audio(audio, orig_sr, target_sr):
    return resample_poly(audio, target_sr, orig_sr)
