"""
游戏框架生成模块 (Deepseek-R1 Phase I)
基于SAD生成结构化主线剧情节点和角色设定
"""

import os
import json
from typing import List, Dict
from pathlib import Path
import sys

# 添加路径以导入其他模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

# 导入统一配置管理
from src.config import get_config, get_section
from src.ai_workflow.utils.gpt_client import gpt
from src.ai_workflow.rag import RAGRetrieval
# 从提示词库加载
from .prompts.framework_prompts import FRAMEWORK_GENERATION_PROMPTS
from src.config.paths import FRAMEWORK_DIR

class GameFrameworkGenerator:
    def __init__(self):
        """初始化游戏框架生成器"""
        # 获取框架生成配置
        self.framework_config = get_section('generators', 'framework_generation')

        # 设置日志
        import logging
        self.logger = logging.getLogger(__name__)

        # 从配置获取默认设置
        self.max_nodes = self.framework_config.get('max_nodes', 10)
        self.default_character = self.framework_config.get('default_character', {
            "name": "主角",
            "description": "一位对文化遗产感兴趣的年轻人",
            "details": ["平凡的背景", "好奇心强", "容易代入"]
        })
        self.default_node_config = self.framework_config.get('default_node', {
            "description_template": "主角开始探索{theme}的旅程",
            "key_events": ["初次接触主题", "建立基本设定"],
            "characters": ["主角"]
        })
        self.rag = RAGRetrieval()

        # 输出目录
        self.framework_dir = FRAMEWORK_DIR
        os.makedirs(self.framework_dir, exist_ok=True)

        # 加载提示词
        self.prompts = FRAMEWORK_GENERATION_PROMPTS

    def generate_story_framework(self, theme: str = None, reference_materials: List[str] = None,
                               inspiration_analyses: List[Dict] = None) -> Dict:
        """
        生成游戏主线剧情框架和角色设定
        使用新的三阶段流程：灵感勘探 -> 灵感聚合 -> 骨架生成
        """
        print("开始生成游戏框架 (Phase I)...")

        # 1. 收集参考资料（如果没有提供灵感分析）
        if inspiration_analyses is None:
            context = self.collect_reference_context(theme, reference_materials)
            inspiration_materials = None
        else:
            # 2. 聚合灵感分析结果
            inspiration_materials = self.aggregate_inspirations(inspiration_analyses)
            context = self.collect_reference_context(theme, reference_materials)

        # 3. 生成主线剧情骨架（使用聚合的灵感素材）
        story_skeleton = self.generate_story_skeleton_with_inspiration(theme, inspiration_materials, context)

        # 4. 生成角色设定
        characters = self.generate_character_settings(story_skeleton, context)

        # 5. 构建完整框架
        framework = {
            "theme": theme or "文化遗产探索",
            "story_skeleton": story_skeleton,
            "characters": characters,
            "inspiration_materials": inspiration_materials,
            "reference_context": context,
            "generation_metadata": {
                "phase": "Phase I - Framework Generation",
                "model": "Deepseek-R1",
                "timestamp": self.get_current_time()
            }
        }

        # 6. 保存框架
        framework_path = self.save_framework(framework)

        print(f"游戏框架生成完成: {framework_path}")
        return framework

    def aggregate_inspirations(self, analyses_list: List[Dict]) -> Dict:
        """聚合多个灵感分析结果"""
        aggregated = {
            "world_premises": [],
            "dramatic_cores": [],
            "character_archetypes": [],
            "symbolic_resonances": [],
            "narrative_hooks": []
        }

        for i, analysis in enumerate(analyses_list):
            for category in aggregated.keys():
                items = analysis.get(category, [])
                if isinstance(items, list):
                    for item in items:
                        aggregated[category].append({
                            "content": item,
                            "source_id": f"material_{i+1}",
                            "unique_id": f"{category}_{i}_{hash(str(item))}",
                            "weight": self._calculate_relevance(item, category)
                        })

        # 后处理：去重、排序、质量过滤
        return self._process_aggregated_data(aggregated)

    def _calculate_relevance(self, item: str, category: str) -> float:
        """计算灵感项目的相关性评分（简单实现）"""
        # 基于长度和关键词的简单评分
        base_score = min(len(str(item)) / 100, 1.0)  # 长度评分

        # 类别特定的关键词加权
        category_keywords = {
            "world_premises": ["世界", "设定", "背景", "环境"],
            "dramatic_cores": ["冲突", "矛盾", "对立", "张力"],
            "character_archetypes": ["角色", "人物", "性格", "身份"],
            "symbolic_resonances": ["象征", "意义", "隐喻", "深层"],
            "narrative_hooks": ["情境", "开端", "起点", "引子"]
        }

        keywords = category_keywords.get(category, [])
        keyword_score = sum(1 for keyword in keywords if keyword in str(item)) / len(keywords)

        return min(base_score + keyword_score * 0.3, 1.0)

    def _process_aggregated_data(self, aggregated: Dict) -> Dict:
        """处理聚合数据：去重、排序、质量过滤"""
        processed = {}

        for category, items in aggregated.items():
            # 去重（基于内容相似性的简单去重）
            unique_items = []
            seen_contents = set()

            for item in items:
                content_key = str(item["content"]).lower().strip()
                if content_key not in seen_contents and len(content_key) > 10:  # 质量过滤
                    seen_contents.add(content_key)
                    unique_items.append(item)

            # 按权重排序
            unique_items.sort(key=lambda x: x["weight"], reverse=True)

            # 限制数量（保留前N个高质量项目）
            max_items = self.framework_config.get('max_inspiration_items_per_category', 8)
            processed[category] = unique_items[:max_items]

        return processed

    def generate_story_skeleton_with_inspiration(self, theme: str, inspiration_materials: Dict, context: Dict) -> Dict:
        """使用聚合的灵感素材生成主线剧情骨架"""

        # 如果没有灵感素材，回退到原有方法
        if not inspiration_materials:
            return self.generate_story_skeleton(theme, context)

        # 构建灵感素材文本
        inspiration_text = self._build_inspiration_text(inspiration_materials)

        system_prompt = FRAMEWORK_GENERATION_PROMPTS["story_skeleton_with_inspiration"]["system"]
        user_prompt = FRAMEWORK_GENERATION_PROMPTS["story_skeleton_with_inspiration"]["user"].format(
            theme=theme,
            inspiration_materials=inspiration_text
        )

        try:
            response = gpt(system_prompt, user_prompt)
            return self.parse_story_skeleton(response)
        except Exception as e:
            print(f"基于灵感的剧情骨架生成失败: {e}")
            return self.generate_default_skeleton(theme)

    def _build_inspiration_text(self, inspiration_materials: Dict) -> str:
        """构建灵感素材文本"""
        text_parts = []

        for category, items in inspiration_materials.items():
            if items:
                category_names = {
                    "world_premises": "世界前提",
                    "dramatic_cores": "戏剧核心",
                    "character_archetypes": "角色原型",
                    "symbolic_resonances": "象征共鸣",
                    "narrative_hooks": "叙事钩子"
                }

                text_parts.append(f"\n{category_names.get(category, category)}:")
                for item in items[:5]:  # 限制每类显示的数量
                    text_parts.append(f"- {item['content']} (来源: {item['source_id']})")

        return '\n'.join(text_parts)

    def collect_reference_context(self, theme: str, keywords: List[str] = None) -> Dict:
        """收集参考上下文，直接读取summarizer生成的灵感文件"""
        context = {
            "inspiration_summaries": [],
            "theme_related": []
        }

        # 读取summarizer生成的灵感文件
        summaries_dir = Path("summaries")  # summarizer的默认输出目录
        if summaries_dir.exists():
            # 查找所有灵感元数据文件
            for metadata_file in summaries_dir.glob("*_inspirations_metadata.json"):
                try:
                    with open(metadata_file, 'r', encoding='utf-8') as f:
                        segments_data = json.load(f)

                    source_name = metadata_file.stem.replace("_inspirations_metadata", "")

                    for segment in segments_data:
                        if isinstance(segment, dict) and any(key in segment for key in
                            ["world_premises", "dramatic_cores", "character_archetypes",
                             "symbolic_resonances", "narrative_hooks"]):
                            context["inspiration_summaries"].append({
                                "content": segment,
                                "source": source_name
                            })

                except Exception as e:
                    self.logger.warning(f"读取灵感文件失败 {metadata_file}: {e}")

        return context

    def generate_story_skeleton(self, theme: str, context: Dict) -> Dict:
        """生成主线剧情骨架"""
        
        # 构建参考资料文本
        reference_text = self.build_reference_text(context)
        
        system_prompt = FRAMEWORK_GENERATION_PROMPTS["story_skeleton"]["system"]
        user_prompt = FRAMEWORK_GENERATION_PROMPTS["story_skeleton"]["user"].format(
            theme=theme,
            reference_text=reference_text
        )

        try:
            response = gpt(system_prompt, user_prompt)
            return self.parse_story_skeleton(response)
        except Exception as e:
            print(f"剧情骨架生成失败: {e}")
            return self.generate_default_skeleton(theme)

    def parse_story_skeleton(self, response: str) -> Dict:
        """解析剧情骨架响应（JSON格式）"""
        try:
            # 尝试直接解析JSON
            skeleton = json.loads(response.strip())

            # 验证必要的字段
            if not isinstance(skeleton, dict):
                raise ValueError("响应不是有效的字典格式")

            # 确保必要的字段存在
            if "mainline_nodes" not in skeleton:
                skeleton["mainline_nodes"] = []
            if "sideline_nodes" not in skeleton:
                skeleton["sideline_nodes"] = []
            if "node_relationships" not in skeleton:
                skeleton["node_relationships"] = []

            # 验证节点格式
            for node in skeleton["mainline_nodes"] + skeleton["sideline_nodes"]:
                if not isinstance(node, dict) or "node_id" not in node:
                    raise ValueError("节点格式不正确")

                # 确保必要字段存在
                node.setdefault("description", "")
                node.setdefault("key_events", [])
                node.setdefault("characters", [])

            return skeleton

        except (json.JSONDecodeError, ValueError) as e:
            print(f"JSON解析失败: {e}")
            print(f"原始响应: {response[:200]}...")
            # 尝试从文本中提取信息作为后备方案
            return self._parse_text_fallback(response)

    def _parse_text_fallback(self, response: str) -> Dict:
        """文本解析后备方案"""
        skeleton = {
            "mainline_nodes": [],
            "sideline_nodes": [],
            "node_relationships": []
        }

        lines = response.split('\n')
        current_node = None

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 检测节点编号
            if line.startswith(('A', 'B', 'C', 'D', 'E', 'F')) and ':' in line:
                if current_node:
                    # 保存前一个节点
                    if current_node["node_id"].startswith('A'):
                        skeleton["mainline_nodes"].append(current_node)
                    else:
                        skeleton["sideline_nodes"].append(current_node)

                # 开始新节点
                parts = line.split(':', 1)
                node_id = parts[0].strip()
                description = parts[1].strip() if len(parts) > 1 else ""

                current_node = {
                    "node_id": node_id,
                    "description": description,
                    "key_events": [],
                    "characters": []
                }
            elif current_node and line:
                # 添加到当前节点的详细信息
                if "关键事件" in line or "事件" in line:
                    current_node["key_events"].append(line)
                elif "角色" in line or "人物" in line:
                    current_node["characters"].append(line)
                else:
                    # 添加到描述中（忽略参考资料相关内容）
                    if not ("参考" in line or "引用" in line):
                        current_node["description"] += " " + line

        # 保存最后一个节点
        if current_node:
            if current_node["node_id"].startswith('A'):
                skeleton["mainline_nodes"].append(current_node)
            else:
                skeleton["sideline_nodes"].append(current_node)

        return skeleton

    def generate_character_settings(self, story_skeleton: Dict, context: Dict) -> List[Dict]:
        """生成角色设定"""
        
        # 从剧情骨架中提取角色信息
        mentioned_characters = set()
        for node in story_skeleton.get("mainline_nodes", []):
            for char_info in node.get("characters", []):
                # 简单的角色名提取
                if ":" in char_info:
                    char_name = char_info.split(":")[0].strip()
                    mentioned_characters.add(char_name)
        
        reference_text = self.build_reference_text(context)
        
        system_prompt = FRAMEWORK_GENERATION_PROMPTS["character_settings"]["system"]
        user_prompt = FRAMEWORK_GENERATION_PROMPTS["character_settings"]["user"].format(
            mentioned_characters=', '.join(mentioned_characters),
            reference_text=reference_text
        )

        try:
            response = gpt(system_prompt, user_prompt)
            return self.parse_character_settings(response)
        except Exception as e:
            print(f"角色设定生成失败: {e}")
            return self.generate_default_characters()

    def parse_character_settings(self, response: str) -> List[Dict]:
        """解析角色设定响应（JSON格式）"""
        try:
            # 尝试直接解析JSON
            characters = json.loads(response.strip())

            # 验证格式
            if not isinstance(characters, list):
                raise ValueError("响应不是有效的列表格式")

            # 验证每个角色的格式并补充缺失字段
            for char in characters:
                if not isinstance(char, dict) or "name" not in char:
                    raise ValueError("角色格式不正确")

                # 确保必要字段存在
                char.setdefault("age", 25)
                char.setdefault("gender", "未知")
                char.setdefault("appearance", "")
                char.setdefault("personality", "")
                char.setdefault("background", "")
                char.setdefault("motivation", "")
                char.setdefault("role", "")
                char.setdefault("relationships", {})
                char.setdefault("is_protagonist", False)

            return characters

        except (json.JSONDecodeError, ValueError) as e:
            print(f"角色设定JSON解析失败: {e}")
            print(f"原始响应: {response[:200]}...")
            # 使用后备文本解析方案
            return self._parse_character_text_fallback(response)

    def _parse_character_text_fallback(self, response: str) -> List[Dict]:
        """角色设定文本解析后备方案"""
        characters = []
        lines = response.split('\n')
        current_character = None

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 检测角色名
            if ':' in line and not line.startswith(' '):
                if current_character:
                    characters.append(current_character)

                char_name = line.split(':')[0].strip()
                char_description = line.split(':', 1)[1].strip() if ':' in line else ""

                current_character = {
                    "name": char_name,
                    "description": char_description,
                    "details": []
                }
            elif current_character and line:
                current_character["details"].append(line)
        
        if current_character:
            characters.append(current_character)
        
        return characters

    def build_reference_text(self, context: Dict) -> str:
        """构建参考资料文本"""
        text_parts = []

        # 从配置获取限制参数
        max_context_items = self.framework_config.get('max_context_items', 10)

        # 灵感摘要
        if context.get("inspiration_summaries"):
            text_parts.append("故事灵感素材：")
            for item in context["inspiration_summaries"][:max_context_items]:
                content = item['content']
                source = item['source']

                # 提取各个维度的灵感
                for category in ["world_premises", "dramatic_cores", "character_archetypes",
                               "symbolic_resonances", "narrative_hooks"]:
                    if category in content and content[category]:
                        category_name = {
                            "world_premises": "世界前提",
                            "dramatic_cores": "戏剧核心",
                            "character_archetypes": "角色原型",
                            "symbolic_resonances": "象征共鸣",
                            "narrative_hooks": "叙事钩子"
                        }.get(category, category)

                        text_parts.append(f"\n{category_name} (来源: {source}):")
                        for premise in content[category][:3]:  # 限制每类显示数量
                            text_parts.append(f"- {premise}")

        return '\n'.join(text_parts)

    def generate_default_skeleton(self, theme: str) -> Dict:
        """生成默认剧情骨架"""
        description = self.default_node_config['description_template'].format(theme=theme)

        return {
            "mainline_nodes": [
                {
                    "node_id": "A1",
                    "description": description,
                    "key_events": self.default_node_config['key_events'].copy(),
                    "characters": self.default_node_config['characters'].copy()
                }
            ],
            "sideline_nodes": [],
            "node_relationships": []
        }

    def generate_default_characters(self) -> List[Dict]:
        """生成默认角色设定"""
        return [self.default_character.copy()]

    def save_framework(self, framework: Dict) -> str:
        """保存游戏框架"""
        timestamp = self.get_current_time().replace(':', '-').replace(' ', '_')
        filename = f"game_framework_{timestamp}.json"
        filepath = os.path.join(self.framework_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(framework, f, ensure_ascii=False, indent=2)
        
        # 同时保存为最新版本
        latest_path = os.path.join(self.framework_dir, "latest_framework.json")
        with open(latest_path, 'w', encoding='utf-8') as f:
            json.dump(framework, f, ensure_ascii=False, indent=2)
        
        return filepath

    def load_latest_framework(self) -> Dict:
        """加载最新的游戏框架"""
        latest_path = os.path.join(self.framework_dir, "latest_framework.json")
        if os.path.exists(latest_path):
            with open(latest_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}

    def get_current_time(self) -> str:
        """获取当前时间字符串"""
        import datetime
        return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
