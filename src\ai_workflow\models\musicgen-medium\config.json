{"_commit_hash": null, "architectures": ["MusicgenForConditionalGeneration"], "audio_encoder": {"_name_or_path": "facebook/encodec_32khz", "add_cross_attention": false, "architectures": ["EncodecModel"], "audio_channels": 1, "bad_words_ids": null, "begin_suppress_tokens": null, "bos_token_id": null, "chunk_length_s": null, "chunk_size_feed_forward": 0, "codebook_dim": 128, "codebook_size": 2048, "compress": 2, "cross_attention_hidden_size": null, "decoder_start_token_id": null, "dilation_growth_rate": 2, "diversity_penalty": 0.0, "do_sample": false, "early_stopping": false, "encoder_no_repeat_ngram_size": 0, "eos_token_id": null, "exponential_decay_length_penalty": null, "finetuning_task": null, "forced_bos_token_id": null, "forced_eos_token_id": null, "hidden_size": 128, "id2label": {"0": "LABEL_0", "1": "LABEL_1"}, "is_decoder": false, "is_encoder_decoder": false, "kernel_size": 7, "label2id": {"LABEL_0": 0, "LABEL_1": 1}, "last_kernel_size": 7, "length_penalty": 1.0, "max_length": 20, "min_length": 0, "model_type": "encodec", "no_repeat_ngram_size": 0, "norm_type": "weight_norm", "normalize": false, "num_beam_groups": 1, "num_beams": 1, "num_filters": 64, "num_lstm_layers": 2, "num_residual_layers": 1, "num_return_sequences": 1, "output_attentions": false, "output_hidden_states": false, "output_scores": false, "overlap": null, "pad_mode": "reflect", "pad_token_id": null, "prefix": null, "problem_type": null, "pruned_heads": {}, "remove_invalid_values": false, "repetition_penalty": 1.0, "residual_kernel_size": 3, "return_dict": true, "return_dict_in_generate": false, "sampling_rate": 32000, "sep_token_id": null, "suppress_tokens": null, "target_bandwidths": [2.2], "task_specific_params": null, "temperature": 1.0, "tf_legacy_loss": false, "tie_encoder_decoder": false, "tie_word_embeddings": true, "tokenizer_class": null, "top_k": 50, "top_p": 1.0, "torch_dtype": "float32", "torchscript": false, "transformers_version": "4.31.0.dev0", "trim_right_ratio": 1.0, "typical_p": 1.0, "upsampling_ratios": [8, 5, 4, 4], "use_bfloat16": false, "use_causal_conv": false, "use_conv_shortcut": false}, "decoder": {"_name_or_path": "", "activation_dropout": 0.0, "activation_function": "gelu", "add_cross_attention": false, "architectures": null, "attention_dropout": 0.0, "bad_words_ids": null, "begin_suppress_tokens": null, "bos_token_id": 2048, "chunk_size_feed_forward": 0, "classifier_dropout": 0.0, "cross_attention_hidden_size": null, "decoder_start_token_id": null, "diversity_penalty": 0.0, "do_sample": false, "dropout": 0.1, "early_stopping": false, "encoder_no_repeat_ngram_size": 0, "eos_token_id": null, "exponential_decay_length_penalty": null, "ffn_dim": 6144, "finetuning_task": null, "forced_bos_token_id": null, "forced_eos_token_id": null, "hidden_size": 1536, "id2label": {"0": "LABEL_0", "1": "LABEL_1"}, "initializer_factor": 0.02, "is_decoder": false, "is_encoder_decoder": false, "label2id": {"LABEL_0": 0, "LABEL_1": 1}, "layerdrop": 0.0, "length_penalty": 1.0, "max_length": 20, "max_position_embeddings": 2048, "min_length": 0, "model_type": "musicgen_decoder", "no_repeat_ngram_size": 0, "num_attention_heads": 24, "num_beam_groups": 1, "num_beams": 1, "num_codebooks": 4, "num_hidden_layers": 48, "num_return_sequences": 1, "output_attentions": false, "output_hidden_states": false, "output_scores": false, "pad_token_id": 2048, "prefix": null, "problem_type": null, "pruned_heads": {}, "remove_invalid_values": false, "repetition_penalty": 1.0, "return_dict": true, "return_dict_in_generate": false, "scale_embedding": false, "sep_token_id": null, "suppress_tokens": null, "task_specific_params": null, "temperature": 1.0, "tf_legacy_loss": false, "tie_encoder_decoder": false, "tie_word_embeddings": false, "tokenizer_class": null, "top_k": 50, "top_p": 1.0, "torch_dtype": null, "torchscript": false, "transformers_version": "4.31.0.dev0", "typical_p": 1.0, "use_bfloat16": false, "use_cache": true, "vocab_size": 2048}, "is_encoder_decoder": true, "model_type": "musicgen", "text_encoder": {"_name_or_path": "t5-base", "add_cross_attention": false, "architectures": ["T5ForConditionalGeneration"], "bad_words_ids": null, "begin_suppress_tokens": null, "bos_token_id": null, "chunk_size_feed_forward": 0, "cross_attention_hidden_size": null, "d_ff": 3072, "d_kv": 64, "d_model": 768, "decoder_start_token_id": 0, "dense_act_fn": "relu", "diversity_penalty": 0.0, "do_sample": false, "dropout_rate": 0.1, "early_stopping": false, "encoder_no_repeat_ngram_size": 0, "eos_token_id": 1, "exponential_decay_length_penalty": null, "feed_forward_proj": "relu", "finetuning_task": null, "forced_bos_token_id": null, "forced_eos_token_id": null, "id2label": {"0": "LABEL_0", "1": "LABEL_1"}, "initializer_factor": 1.0, "is_decoder": false, "is_encoder_decoder": true, "is_gated_act": false, "label2id": {"LABEL_0": 0, "LABEL_1": 1}, "layer_norm_epsilon": 1e-06, "length_penalty": 1.0, "max_length": 20, "min_length": 0, "model_type": "t5", "n_positions": 512, "no_repeat_ngram_size": 0, "num_beam_groups": 1, "num_beams": 1, "num_decoder_layers": 12, "num_heads": 12, "num_layers": 12, "num_return_sequences": 1, "output_attentions": false, "output_hidden_states": false, "output_past": true, "output_scores": false, "pad_token_id": 0, "prefix": null, "problem_type": null, "pruned_heads": {}, "relative_attention_max_distance": 128, "relative_attention_num_buckets": 32, "remove_invalid_values": false, "repetition_penalty": 1.0, "return_dict": true, "return_dict_in_generate": false, "sep_token_id": null, "suppress_tokens": null, "task_specific_params": {"summarization": {"early_stopping": true, "length_penalty": 2.0, "max_length": 200, "min_length": 30, "no_repeat_ngram_size": 3, "num_beams": 4, "prefix": "summarize: "}, "translation_en_to_de": {"early_stopping": true, "max_length": 300, "num_beams": 4, "prefix": "translate English to German: "}, "translation_en_to_fr": {"early_stopping": true, "max_length": 300, "num_beams": 4, "prefix": "translate English to French: "}, "translation_en_to_ro": {"early_stopping": true, "max_length": 300, "num_beams": 4, "prefix": "translate English to Romanian: "}}, "temperature": 1.0, "tf_legacy_loss": false, "tie_encoder_decoder": false, "tie_word_embeddings": true, "tokenizer_class": null, "top_k": 50, "top_p": 1.0, "torch_dtype": null, "torchscript": false, "transformers_version": "4.31.0.dev0", "typical_p": 1.0, "use_bfloat16": false, "use_cache": true, "vocab_size": 32128}, "torch_dtype": "float32", "transformers_version": null}