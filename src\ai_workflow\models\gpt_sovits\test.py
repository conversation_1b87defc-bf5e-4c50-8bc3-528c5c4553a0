import sys,os
import torch
ROOT_DIR = os.path.dirname(os.path.abspath(__file__))
if ROOT_DIR not in sys.path:
    sys.path.insert(0, ROOT_DIR)

from infer import GPTSoVITSInference

from scipy.io import wavfile
output_path = os.path.join('playground', 'output.wav')
# os.makedirs(output_path,exist_ok=True)
inference = GPTSoVITSInference(
    bert_path="./pretrained_models/chinese-roberta-wwm-ext-large",
    cnhubert_base_path="./pretrained_models/chinese-hubert-base",
    is_half=False, # Seems not support for the Colab environment
    device="cuda" if torch.cuda.is_available() else "cpu"
)
inference.load_sovits("./pretrained_models/s2G488k.pth")
inference.load_gpt(
    "./pretrained_models/s1bert25hz-2kh-longer-epoch=68e-step=50232.ckpt"
)
prompt_text = "哈基米哦南北绿豆"
inference.set_prompt_audio(
    prompt_audio_path=r"C:\Users\<USER>\xwechat_files\wxid_d1jpoe2dok7522_6cc7\msg\file\2025-08\哈基米哦南北绿豆.wav",
    prompt_text=prompt_text,
)

sample_rate, data = inference.get_tts_wav(
    text="鲁迅为什么暴打周树人？？？这是一个问题\n\n自古以来，文人相轻，鲁迅和周树人也不例外。鲁迅和周树人是中国现代文学史上的两位伟大作家，他们的文学成就都是不可磨灭的。但是，鲁迅和周树人之间的关系并不和谐，两人之间曾经发生过一次激烈的冲突，甚至还打了起来。那么，鲁迅为什么会暴打周树人呢？这是一个问题。  ",
)
wavfile.write(output_path, sample_rate, data)
