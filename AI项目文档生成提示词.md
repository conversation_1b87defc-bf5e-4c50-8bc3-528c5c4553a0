# AI项目文档生成提示词

## 用户提示词（User Prompt）

请你作为一名资深的技术文档工程师，为"遗游记 文化遗产游戏生成系统"项目编写一份完整、专业的项目说明文档。

### 项目背景
这是一个基于大模型技术实现的文化遗产严肃游戏生成系统，能够从原始文化素材自动生成完整的Ren'Py视觉小说游戏。项目采用Python开发，集成了多种AI技术栈，包括RAG检索、多模态生成、语音合成等。

### 文档要求
请按照以下结构编写项目说明文档，确保内容详实、逻辑清晰、技术准确：

#### 1. 项目概述 (Project Overview)
- 项目名称、版本信息和核心定位
- 项目的创新价值和应用场景
- 主要功能特性和技术亮点
- 目标用户群体和使用价值

#### 2. 开发理念 (Development Philosophy)
- 设计思想和架构原则
- 技术选型的考虑因素
- 模块化设计理念
- 可扩展性和维护性考虑

#### 3. 技术架构 (Technical Architecture)
- 整体系统架构图和说明
- 核心技术栈详细介绍
- 各模块间的依赖关系
- 数据流向和处理流程

#### 4. 开发框架 (Development Framework)
- Python环境要求和依赖管理
- 项目目录结构详细说明
- 配置管理系统
- 开发工具链和最佳实践

#### 5. 数据库结构 (Database Structure)
- 向量数据库设计（FAISS索引）
- 文本embedding存储结构
- 图像索引和元数据管理
- 配置文件结构说明

#### 6. 核心模块详解 (Core Modules)
- 素材预处理模块（文本、图像）
- RAG检索增强系统
- AI生成器模块（框架、叙事、图像、语音、音乐）
- Ren'Py集成模块
- 主流程控制器

#### 7. API接口说明 (API Documentation)
- 后端服务架构（FastAPI）
- 核心API接口列表和参数说明
- 服务注册机制
- 异步任务管理
- 错误处理和响应格式

#### 8. 部署与运行 (Deployment & Operation)
- 环境配置要求
- 安装部署步骤
- 配置文件设置指南
- 启动和使用方法
- 故障排除指南

#### 9. 使用示例 (Usage Examples)
- 完整游戏生成流程演示
- 各模块独立使用示例
- API调用示例代码
- 前端集成指南

#### 10. 扩展开发 (Extension Development)
- 新功能模块开发指南
- 自定义生成器开发
- 第三方服务集成
- 贡献代码规范

### 写作要求
1. **专业性**：使用准确的技术术语，体现项目的技术深度
2. **完整性**：覆盖项目的所有重要方面，不遗漏关键信息
3. **实用性**：提供具体的使用指导和代码示例
4. **可读性**：结构清晰，层次分明，便于不同技术背景的读者理解
5. **准确性**：确保所有技术细节和配置信息的准确性

### 特别注意
- 重点突出项目的创新性和技术优势
- 详细说明RAG检索系统的设计和实现
- 强调多模态AI生成的技术特色
- 提供完整的API文档和使用示例
- 包含详细的部署和配置指南

请基于以上要求，生成一份高质量的项目说明文档。

---

## 记忆信息 (Memories)

### 项目核心信息
- 项目名称：遗游记 文化遗产游戏生成系统
- 版本：2.0.0
- 主要技术栈：Python 3.10+, FastAPI, RAG, 多模态AI生成
- 核心功能：从文化素材自动生成完整的Ren'Py视觉小说游戏

### 技术架构特点
- 采用模块化设计，包含10个独立功能模块
- 基于RAG检索增强生成技术
- 支持文本、图像、语音、音乐的多模态生成
- 提供CLI和API两种使用方式
- 集成完整的Ren'Py游戏引擎输出

### 核心模块组成
1. 素材预处理模块（文本分段、图像分析）
2. RAG检索系统（文本和图像双模态）
3. 游戏框架生成器（剧情骨架、角色设定）
4. 节点叙事生成器（详细对话和场景）
5. 图像生成器（背景、立绘、CG）
6. 语音生成器（GPT-SoVITS）
7. 音乐生成器（MusicGen）
8. Ren'Py集成器（脚本生成）
9. UI设计模块（界面主题）
10. 主流程控制器（端到端管理）

### 数据存储结构
- 使用FAISS进行向量索引
- 文本embedding统一存储为.npz格式
- 图像索引包含CLIP embeddings
- 配置采用JSON和INI文件管理

### API服务特色
- 基于FastAPI的微服务架构
- 统一的服务注册和调用机制
- 支持同步和异步任务处理
- 完整的任务状态管理
- 自动生成API文档

### 部署要求
- Python 3.10+环境
- GPU推荐（用于AI生成）
- 需要配置多个AI服务API密钥
- 支持Windows、Linux等主流操作系统
