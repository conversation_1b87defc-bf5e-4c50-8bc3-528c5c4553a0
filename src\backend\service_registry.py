"""
服务注册表
实现服务注册和调用机制，提供统一的服务管理
"""

from typing import Callable, Dict, Any, List, Optional
import inspect
from .game_services import GameGenerationServices

# 全局服务注册表
_services: Dict[str, Callable] = {}
_service_descriptions: Dict[str, Dict[str, Any]] = {}

def register_service(name: str, description: str = "", parameters: Optional[Dict[str, Any]] = None):
    """
    服务注册装饰器
    
    Args:
        name: 服务名称
        description: 服务描述
        parameters: 参数说明
    """
    def decorator(func: Callable):
        _services[name] = func
        
        # 获取函数签名
        sig = inspect.signature(func)
        param_info = {}
        for param_name, param in sig.parameters.items():
            if param_name != 'self':  # 跳过self参数
                param_info[param_name] = {
                    "type": str(param.annotation) if param.annotation != inspect.Parameter.empty else "Any",
                    "default": param.default if param.default != inspect.Parameter.empty else None,
                    "required": param.default == inspect.Parameter.empty
                }
        
        _service_descriptions[name] = {
            "description": description,
            "parameters": parameters or param_info,
            "function": func.__name__
        }
        
        return func
    return decorator

def call_service(name: str, **kwargs) -> Dict[str, Any]:
    """
    调用注册的服务
    
    Args:
        name: 服务名称
        **kwargs: 服务参数
        
    Returns:
        服务执行结果
    """
    if name not in _services:
        return {
            "status": "error",
            "message": f"服务 '{name}' 未注册",
            "data": None
        }
    
    try:
        result = _services[name](**kwargs)
        return result
    except Exception as e:
        return {
            "status": "error",
            "message": f"服务调用失败: {str(e)}",
            "data": None
        }

def get_available_services() -> Dict[str, Any]:
    """
    获取所有可用服务列表
    
    Returns:
        服务列表和描述
    """
    return {
        "status": "success",
        "message": "获取服务列表成功",
        "data": {
            "services": _service_descriptions,
            "total_count": len(_services)
        }
    }

def get_service_info(name: str) -> Dict[str, Any]:
    """
    获取特定服务的详细信息
    
    Args:
        name: 服务名称
        
    Returns:
        服务详细信息
    """
    if name not in _services:
        return {
            "status": "error",
            "message": f"服务 '{name}' 不存在",
            "data": None
        }
    
    return {
        "status": "success",
        "message": "获取服务信息成功",
        "data": _service_descriptions[name]
    }

# 初始化服务实例
_game_services = GameGenerationServices()

# 注册所有游戏生成服务
@register_service(
    "generate_complete_game",
    "生成完整的文化遗产严肃游戏",
    {
        "theme": {"type": "str", "description": "游戏主题", "required": True},
        "material_paths": {"type": "List[str]", "description": "素材文件路径列表", "required": True},
        "reference_materials": {"type": "List[str]", "description": "参考材料关键词列表", "required": False}
    }
)
def generate_complete_game_service(theme: str, material_paths: List[str], 
                                 reference_materials: Optional[List[str]] = None):
    """生成完整游戏服务"""
    return _game_services.generate_complete_game(theme, material_paths, reference_materials)

@register_service(
    "test_material_preprocessing",
    "测试素材预处理功能",
    {
        "test_text": {"type": "str", "description": "测试文本内容", "required": False}
    }
)
def test_material_preprocessing_service(test_text: Optional[str] = None):
    """测试素材预处理服务"""
    return _game_services.test_material_preprocessing(test_text)

@register_service(
    "test_rag_retrieval",
    "测试RAG检索功能",
    {
        "query": {"type": "str", "description": "检索查询", "required": False},
        "top_k": {"type": "int", "description": "返回结果数量", "required": False}
    }
)
def test_rag_retrieval_service(query: Optional[str] = None, top_k: int = 3):
    """测试RAG检索服务"""
    return _game_services.test_rag_retrieval(query, top_k)

@register_service(
    "run_system_test",
    "运行系统综合测试",
    {}
)
def run_system_test_service():
    """运行系统测试服务"""
    return _game_services.run_system_test()

@register_service(
    "check_configuration",
    "检查系统配置状态",
    {}
)
def check_configuration_service():
    """检查配置服务"""
    return _game_services.check_configuration()

@register_service(
    "get_system_info",
    "获取系统基本信息",
    {}
)
def get_system_info_service():
    """获取系统信息服务"""
    return _game_services.get_system_info()

@register_service(
    "ping",
    "测试服务连通性",
    {}
)
def ping_service():
    """Ping服务，用于测试连通性"""
    return {
        "status": "success",
        "message": "服务正常运行",
        "data": {
            "timestamp": __import__('time').time(),
            "service_count": len(_services)
        }
    }

# 服务发现相关功能
@register_service(
    "list_services",
    "获取所有可用服务列表",
    {}
)
def list_services_service():
    """列出所有服务"""
    return get_available_services()

@register_service(
    "service_info",
    "获取特定服务的详细信息",
    {
        "service_name": {"type": "str", "description": "服务名称", "required": True}
    }
)
def service_info_service(service_name: str):
    """获取服务信息"""
    return get_service_info(service_name)

@register_service(
    "test_music_generation",
    "测试音乐生成功能",
    {
        "prompt": {"type": "str", "description": "音乐生成提示词", "required": False},
        "style": {"type": "str", "description": "音乐风格 (common/sad/epic/ambient/upbeat)", "required": False}
    }
)
def test_music_generation_service(prompt: Optional[str] = None, style: str = "common"):
    """测试音乐生成服务"""
    return _game_services.test_music_generation(prompt, style)

@register_service(
    "generate_background_music_set",
    "为游戏主题生成一套背景音乐",
    {
        "theme": {"type": "str", "description": "游戏主题", "required": True},
        "scene_count": {"type": "int", "description": "场景数量", "required": False}
    }
)
def generate_background_music_set_service(theme: str, scene_count: int = 5):
    """生成背景音乐集服务"""
    return _game_services.generate_background_music_set(theme, scene_count)
