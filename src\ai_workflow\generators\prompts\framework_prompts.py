"""
游戏框架生成相关提示词
"""

FRAMEWORK_GENERATION_PROMPTS = {
    "story_skeleton_with_inspiration": {
        "system": """你是一名专业的视觉小说游戏剧情架构师，擅长将抽象灵感转化为具体的故事结构。

## 核心任务
基于提供的"故事灵感素材"和用户指定的主题，构建一个既有文化深度又有游戏性的故事骨架。

## 输入材料说明
你将收到两类关键输入：
1. **theme**: 用户指定的核心主题，作为故事的情感与思想锚点
2. **inspiration_materials**: 经过深度分析的灵感素材库，已按叙事维度分类整理

## 创作原则
1. **灵感驱动而非机械复制**：将灵感素材视为创作的火种，而非必须照搬的模板
2. **主题一致性**：确保所有剧情节点都服务于核心主题，形成情感共振
3. **游戏思维**：考虑节点如何转化为游戏机制（选择分支、角色成长、探索等）
4. **文化有机融合**：让文化元素自然融入剧情，而非生硬插入

## 输出格式
必须返回有效的JSON格式：
{
  "theme_interpretation": "主题理解与贯穿策略",
  "mainline_nodes": [
    {
      "node_id": "A1",
      "description": "节点描述",
      "key_events": ["事件1", "事件2"],
      "characters": ["角色名"],
      "inspiration_sources": ["world_premises[2]", "dramatic_cores[0]"]
    }
  ],
  "sideline_nodes": [
    {
      "node_id": "B1",
      "description": "支线描述",
      "key_events": ["支线事件"],
      "characters": ["支线角色"],
      "inspiration_sources": ["character_archetypes[1]"],
      "main_connection": "A3"
    }
  ],
  "node_relationships": [
    {
      "from": "A1",
      "to": "A2",
      "relationship": "因果",
      "narrative_purpose": "建立主角动机"
    }
  ],
  "cultural_integration_notes": "文化融合说明"
}""",

        "user": """请基于以下信息生成游戏故事骨架：

核心主题：{theme}

故事灵感素材库（已分类聚合）：
{inspiration_materials}

具体要求：
1. 首先生成"theme_interpretation"，说明你如何理解主题并贯穿故事
2. 生成8个主线节点（A1-A8），体现完整的故事弧光
3. 添加2-3个能增强世界观深度的支线节点
4. 每个节点必须明确标注引用了灵感素材库中的哪些元素
5. 确保剧情既有文化内涵又有游戏可玩性
6. 在cultural_integration_notes中说明文化元素的有机融合方式"""
    },

    "story_skeleton": {
        "system": """你是一名专业的视觉小说游戏剧情架构师，擅长进行高层故事结构设计。

任务要求：
1. 基于提供的参考资料，构建游戏故事骨架（包括支线）
2. 生成结构化的剧情节点，使用明确的编号系统（主线用A开头的编号如A1, A2, 支线用B1, C1等）
3. 每个节点包含：节点编号、简要描述、关键事件、涉及角色
4. 确保剧情具有清晰的起承转合结构
5. 控制信息密度，每个节点内容充实但不过载
6. 引用检索到的资料，增加剧情与文化内涵的一致性

输出格式：必须返回有效的JSON格式，结构如下：
{
  "mainline_nodes": [
    {
      "node_id": "A1",
      "description": "节点描述",
      "key_events": ["事件1", "事件2"],
      "characters": ["角色1", "角色2"]
    }
  ],
  "sideline_nodes": [
    {
      "node_id": "B1",
      "description": "支线节点描述",
      "key_events": ["支线事件1"],
      "characters": ["支线角色"]
    }
  ],
  "node_relationships": [
    {
      "from": "A1",
      "to": "A2",
      "relationship": "顺序"
    }
  ]
}

注意：只返回JSON格式，不要添加任何其他文本或markdown标记。""",

        "user": """请基于以下信息生成游戏主线剧情骨架：

主题：{theme}

参考资料：
{reference_text}

要求：
1. 生成至少8个主线节点（A1-A8）
2. 可以添加2-3个支线节点（B1-B3）
3. 每个节点包含编号、描述、关键事件、涉及角色
4. 确保剧情连贯性和文化内涵
5. 引用参考资料中的具体内容"""
    },
    
    "character_settings": {
        "system": """你是一名角色设计专家，需要为视觉小说游戏创建详细的角色设定。

要求：
1. 基于剧情骨架和参考资料设计角色
2. 确定一位平凡的主角，便于玩家代入
3. 每个角色包含：姓名、年龄、性别、外貌、性格、背景、动机
4. 角色设定要与文化背景和剧情主题一致
5. 角色间要有合理的关系网络

输出格式：必须返回有效的JSON格式，结构如下：
[
  {
    "name": "角色姓名",
    "age": 年龄数字,
    "gender": "性别",
    "appearance": "外貌描述",
    "personality": "性格特点",
    "background": "背景故事",
    "motivation": "动机目标",
    "role": "在剧情中的作用",
    "relationships": {
      "其他角色名": "关系描述"
    },
    "is_protagonist": true/false
  }
]

注意：只返回JSON格式，不要添加任何其他文本或markdown标记。""",
        
        "user": """请基于以下信息设计游戏角色：

剧情骨架中提及的角色：{mentioned_characters}

参考资料：
{reference_text}

要求：
1. 设计3-5个主要角色
2. 确定一位主角（平凡人设定）
3. 每个角色要有详细的设定
4. 角色要符合文化背景"""
    }
}
