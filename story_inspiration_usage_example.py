#!/usr/bin/env python3
"""
故事灵感勘探系统使用示例

展示如何使用新的三阶段流程：
1. 故事灵感勘探
2. 灵感聚合
3. 故事骨架生成
"""

import sys
import os
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.ai_workflow.preprocess.text_preprocessor.tools.summarizer import Summarizer
from src.ai_workflow.generators.game_framework_generator import GameFrameworkGenerator

def main():
    """主函数：演示完整的三阶段流程"""
    
    # 示例文本材料
    text_materials = [
        """
        在中国古代，茶文化不仅仅是一种饮食习惯，更是一种精神追求。从唐代的茶圣陆羽开始，
        茶道就被赋予了深刻的哲学内涵。一片茶叶，从采摘到制作，再到品饮，每一个环节都蕴含着
        对自然的敬畏和对生活的感悟。然而，在现代快节奏的生活中，这种慢生活的艺术正在逐渐消失。
        """,
        
        """
        苏州园林是中国古典园林的杰出代表，它们不仅是建筑艺术的瑰宝，更是中国文人精神世界的
        物化体现。每一座园林都是一个微缩的宇宙，山水、花木、建筑相互呼应，营造出诗意的空间。
        园林的主人往往是文人雅士，他们在这里读书、作画、会友，追求着"虽由人作，宛自天开"的
        理想境界。
        """,
        
        """
        京剧是中国传统戏曲的集大成者，它融合了唱、念、做、打四种艺术形式。每一个动作、每一个
        眼神都有着严格的程式，这些程式经过几代艺术家的传承和完善，形成了独特的表演体系。
        然而，随着娱乐方式的多样化，京剧面临着观众流失、传承困难的挑战。
        """
    ]
    
    print("=== 故事灵感勘探系统演示 ===\n")
    
    # 第一阶段：故事灵感勘探
    print("第一阶段：故事灵感勘探")
    print("-" * 40)
    
    summarizer = Summarizer(output_dir="demo_output")
    inspiration_analyses = []
    
    for i, text in enumerate(text_materials, 1):
        print(f"正在分析文本材料 {i}...")
        try:
            inspiration = summarizer.extract_story_inspiration(text)
            inspiration_analyses.append(inspiration)
            print(f"文本材料 {i} 灵感勘探完成")
            
            # 显示部分结果
            print(f"  世界前提: {inspiration.get('world_premises', [])[:2]}")
            print(f"  戏剧核心: {inspiration.get('dramatic_cores', [])[:2]}")
            print()
            
        except Exception as e:
            print(f"文本材料 {i} 灵感勘探失败: {e}")
    
    # 第二阶段：灵感聚合
    print("第二阶段：灵感聚合")
    print("-" * 40)
    
    generator = GameFrameworkGenerator()
    
    try:
        aggregated_inspirations = generator.aggregate_inspirations(inspiration_analyses)
        print("灵感聚合完成")
        
        # 显示聚合结果统计
        for category, items in aggregated_inspirations.items():
            print(f"  {category}: {len(items)} 个项目")
        print()
        
    except Exception as e:
        print(f"灵感聚合失败: {e}")
        return
    
    # 第三阶段：故事骨架生成
    print("第三阶段：故事骨架生成")
    print("-" * 40)
    
    theme = "传统文化的传承与创新"
    
    try:
        framework = generator.generate_story_framework(
            theme=theme,
            inspiration_analyses=inspiration_analyses
        )
        
        print("故事骨架生成完成")
        print(f"主题: {framework.get('theme')}")
        
        # 显示故事骨架概要
        story_skeleton = framework.get('story_skeleton', {})
        mainline_nodes = story_skeleton.get('mainline_nodes', [])
        sideline_nodes = story_skeleton.get('sideline_nodes', [])
        
        print(f"主线节点数量: {len(mainline_nodes)}")
        print(f"支线节点数量: {len(sideline_nodes)}")
        
        # 显示前几个主线节点
        print("\n主线节点预览:")
        for node in mainline_nodes[:3]:
            print(f"  {node.get('node_id')}: {node.get('description', '')[:50]}...")
        
        # 保存完整结果
        output_file = "demo_output/complete_framework.json"
        os.makedirs("demo_output", exist_ok=True)
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(framework, f, ensure_ascii=False, indent=2)
        
        print(f"\n完整结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"故事骨架生成失败: {e}")
        return
    
    print("\n=== 演示完成 ===")
    print("新的三阶段流程成功运行！")

if __name__ == "__main__":
    main()
