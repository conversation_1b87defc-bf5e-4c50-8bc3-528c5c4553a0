"""
游戏生成服务模块
将CLI逻辑提取为纯函数接口，供后端API调用
"""

import os
import sys
import time
import configparser
from typing import List, Dict, Optional, Any

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from src.ai_workflow.main_pipeline_controller import MainPipelineController

class GameGenerationServices:
    """游戏生成服务类"""
    
    def __init__(self):
        """初始化服务"""
        self.config = configparser.ConfigParser()
        config_path = os.path.join(project_root, "config.ini")
        self.config.read(config_path, encoding='utf-8')
    
    def generate_complete_game(self, theme: str, material_paths: List[str], 
                             reference_materials: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        生成完整游戏
        
        Args:
            theme: 游戏主题
            material_paths: 素材文件路径列表
            reference_materials: 参考材料关键词列表
            
        Returns:
            生成结果字典
        """
        try:
            # 验证输入参数
            if not theme:
                theme = "雷州石狗文化遗产"
            
            if not material_paths:
                material_paths = self._create_example_materials()
            
            # 初始化控制器
            controller = MainPipelineController()
            
            # 执行完整生成流程
            start_time = time.time()
            results = controller.generate_complete_game(
                material_paths=material_paths,
                theme=theme,
                keywords=reference_materials
            )
            end_time = time.time()
            
            # 构建返回结果
            response = {
                "status": "success",
                "message": "游戏生成完成",
                "data": {
                    "generation_time": end_time - start_time,
                    "output_directory": results['final_output']['game_directory'],
                    "statistics": results.get('completion_report', {}).get('content_statistics', {}),
                    "results": results
                }
            }
            
            return response
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"游戏生成失败: {str(e)}",
                "data": None
            }
    
    def test_material_preprocessing(self, test_text: Optional[str] = None) -> Dict[str, Any]:
        """
        测试素材预处理
        
        Args:
            test_text: 测试文本，如果为空则使用默认文本
            
        Returns:
            处理结果字典
        """
        try:
            from src.ai_workflow.preprocess.material_preprocessor import MaterialPreprocessor
            
            if not test_text:
                test_text = """
                雷州石狗是广东雷州半岛特有的文化遗产。
                这些石狗雕像承载着深厚的历史文化内涵，
                被当地人视为守护神，具有驱邪避灾的功效。
                每个村庄都有自己的石狗传说和故事。
                """
            
            preprocessor = MaterialPreprocessor()
            result = preprocessor.process_text_material(test_text, "测试素材")
            
            return {
                "status": "success",
                "message": "素材预处理完成",
                "data": {
                    "sad_file": result,
                    "processed_text": test_text
                }
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"素材预处理失败: {str(e)}",
                "data": None
            }
    
    def test_rag_retrieval(self, query: Optional[str] = None, top_k: int = 3) -> Dict[str, Any]:
        """
        测试RAG检索
        
        Args:
            query: 检索查询，如果为空则使用默认查询
            top_k: 返回结果数量
            
        Returns:
            检索结果字典
        """
        try:
            from src.ai_workflow.rag.rag_retrieval import RAGRetrieval
            
            if not query:
                query = "石狗传说"
            
            rag = RAGRetrieval()
            
            # 获取统计信息
            stats = rag.get_statistics()
            
            # 执行检索
            results = rag.search(query, top_k=top_k)
            
            return {
                "status": "success",
                "message": "RAG检索完成",
                "data": {
                    "query": query,
                    "statistics": stats,
                    "results": [
                        {
                            "similarity": result['similarity'],
                            "content": result['document'][:200] + "..." if len(result['document']) > 200 else result['document'],
                            "source": result['metadata'].get('material_name', '未知')
                        }
                        for result in results
                    ]
                }
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"RAG检索失败: {str(e)}",
                "data": None
            }

    def test_music_generation(self, prompt: Optional[str] = None, style: str = "common") -> Dict[str, Any]:
        """
        测试音乐生成

        Args:
            prompt: 音乐生成提示词，如果为空则使用默认提示词
            style: 音乐风格

        Returns:
            生成结果字典
        """
        try:
            from src.ai_workflow.generators.music_generator import MusicGenerator

            if not prompt:
                prompt = "peaceful background music for cultural heritage game"

            generator = MusicGenerator()

            # 检查生成器状态
            status = generator.get_generation_status()

            if not status.get("enabled", False):
                return {
                    "status": "disabled",
                    "message": "音乐生成功能已禁用",
                    "data": {
                        "generator_status": status
                    }
                }

            # 生成测试音乐
            test_filename = f"test_music_{int(__import__('time').time())}"
            result = generator.generate_music(prompt, test_filename, style)

            return {
                "status": "success" if result == "ok" else "error",
                "message": "音乐生成测试完成" if result == "ok" else f"音乐生成失败: {result}",
                "data": {
                    "prompt": prompt,
                    "style": style,
                    "filename": test_filename,
                    "result": result,
                    "generator_status": status,
                    "output_path": f"{status.get('output_directory', 'music')}/{test_filename}.mp3"
                }
            }

        except Exception as e:
            return {
                "status": "error",
                "message": f"音乐生成测试失败: {str(e)}",
                "data": None
            }

    def generate_background_music_set(self, theme: str, scene_count: int = 5) -> Dict[str, Any]:
        """
        为游戏主题生成一套背景音乐

        Args:
            theme: 游戏主题
            scene_count: 场景数量

        Returns:
            生成结果字典
        """
        try:
            from src.ai_workflow.generators.music_generator import MusicGenerator

            generator = MusicGenerator()

            # 检查生成器状态
            status = generator.get_generation_status()

            if not status.get("enabled", False):
                return {
                    "status": "disabled",
                    "message": "音乐生成功能已禁用",
                    "data": None
                }

            # 生成背景音乐集
            results = generator.generate_background_music_set(theme, scene_count)

            # 统计生成结果
            success_count = sum(1 for r in results if r.get("status") == "ok")

            return {
                "status": "success" if success_count > 0 else "error",
                "message": f"背景音乐生成完成，成功 {success_count}/{len(results)} 首",
                "data": {
                    "theme": theme,
                    "scene_count": scene_count,
                    "success_count": success_count,
                    "total_count": len(results),
                    "music_files": results,
                    "generator_status": status
                }
            }

        except Exception as e:
            return {
                "status": "error",
                "message": f"背景音乐生成失败: {str(e)}",
                "data": None
            }
    
    def run_system_test(self) -> Dict[str, Any]:
        """
        运行系统测试
        
        Returns:
            测试结果字典
        """
        try:
            from src.ai_workflow.tests.test_system import GameGenerationTester
            
            tester = GameGenerationTester()
            results = tester.run_comprehensive_test()
            
            return {
                "status": "success",
                "message": "系统测试完成",
                "data": {
                    "overall_success_rate": results.get('overall_success_rate', 0),
                    "total_duration": results.get('total_duration', 0),
                    "recommendations": results.get('recommendations', []),
                    "test_passed": results.get('overall_success_rate', 0) >= 0.8
                }
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"系统测试失败: {str(e)}",
                "data": None
            }
    
    def check_configuration(self) -> Dict[str, Any]:
        """
        检查配置
        
        Returns:
            配置检查结果字典
        """
        try:
            # 检查关键配置项
            config_items = [
                ("CHATGPT", "gpt_key", "GPT API密钥"),
                ("CHATGPT", "base_url", "GPT API地址"),
                ("CHATGPT", "model", "GPT模型"),
                ("AI绘画", "if_cloud", "图像生成模式"),
                ("SOVITS", "if_cloud", "语音生成模式"),
                ("AI音乐", "if_on", "音乐生成开关")
            ]
            
            config_status = []
            for section, key, description in config_items:
                try:
                    value = self.config.get(section, key, fallback="")
                    status = "已配置" if value else "未配置"
                    config_status.append({
                        "name": description,
                        "status": status,
                        "value": value[:20] + "..." if value and len(value) > 20 else value
                    })
                except Exception as e:
                    config_status.append({
                        "name": description,
                        "status": "配置错误",
                        "value": str(e)
                    })
            
            # 检查依赖
            dependencies = [
                ("spacy", "文本处理"),
                ("PIL", "图像处理"),
                ("requests", "网络请求"),
                ("numpy", "数值计算"),
                ("sklearn", "机器学习")
            ]
            
            dependency_status = []
            for package, description in dependencies:
                try:
                    __import__(package)
                    dependency_status.append({
                        "name": f"{description} ({package})",
                        "status": "已安装"
                    })
                except ImportError:
                    dependency_status.append({
                        "name": f"{description} ({package})",
                        "status": "未安装"
                    })
            
            return {
                "status": "success",
                "message": "配置检查完成",
                "data": {
                    "config_status": config_status,
                    "dependency_status": dependency_status
                }
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"配置检查失败: {str(e)}",
                "data": None
            }
    
    def get_system_info(self) -> Dict[str, Any]:
        """
        获取系统信息
        
        Returns:
            系统信息字典
        """
        return {
            "status": "success",
            "message": "系统信息获取成功",
            "data": {
                "version": "2.0.0",
                "title": "遗游记 文化遗产游戏生成系统",
                "description": "基于大纲文档实现的完整文化遗产严肃游戏生成系统",
                "features": [
                    "素材预处理 - 文本分段、摘要生成、图像语义分析",
                    "RAG检索系统 - 智能检索相关文化内容",
                    "游戏框架生成 - 结构化剧情节点和角色设定",
                    "节点叙事生成 - 详细对话和场景描述",
                    "图像生成 - 基于文化背景的智能图像生成",
                    "音频生成 - 背景音乐和角色语音合成",
                    "Ren'Py整合 - 完整游戏脚本生成",
                    "UI设计 - 文化特色界面和交互逻辑"
                ]
            }
        }
    
    def _create_example_materials(self) -> List[str]:
        """创建示例素材"""
        example_dir = "example_materials"
        os.makedirs(example_dir, exist_ok=True)
        
        # 创建示例文本
        example_text = """
雷州石狗文化遗产

雷州石狗是广东雷州半岛独特的文化遗产，承载着深厚的历史文化内涵。
这些石狗雕像不仅是艺术品，更是当地人民精神信仰的象征。

历史背景：
石狗文化起源于古代雷州，与当地的民间信仰和传说紧密相关。
据传说，石狗具有驱邪避灾、保佑平安的神奇功效。

文化意义：
每个村庄都有自己的石狗，村民们会定期祭拜，祈求风调雨顺。
石狗见证了雷州半岛的历史变迁，是珍贵的文化遗产。

传说故事：
相传有一只神犬为了保护村民，化身为石狗永远守护着这片土地。
当村民遇到困难时，石狗会显灵相助，因此被奉为守护神。
"""
        
        example_path = os.path.join(example_dir, "雷州石狗文化.txt")
        with open(example_path, 'w', encoding='utf-8') as f:
            f.write(example_text)
        
        return [example_path]
