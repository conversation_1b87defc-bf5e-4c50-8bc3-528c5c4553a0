## 3. 技术架构 (Technical Architecture)

### 整体系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    用户交互层                                │
│  ┌─────────────┐  ┌─────────────┐                          │
│  │  CLI界面    │  │  FastAPI    │                          │
│  │ (launcher)  │  │  后端服务   │                          │
│  └─────────────┘  └─────────────┘                          │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    服务编排层                                │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │           主流程控制器 (MainPipelineController)          │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    核心业务层                                │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│ │素材预处理   │ │RAG检索系统  │ │AI生成器群   │ │Ren'Py   │ │
│ │模块         │ │(文本+图像)  │ │(8个生成器)  │ │集成器   │ │
│ └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    数据存储层                                │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│ │FAISS向量库  │ │JSON配置     │ │temp中间文件 │ │output   │ │
│ │(文本+图像)  │ │(5个配置文件)│ │存储         │ │最终产物 │ │
│ └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 核心技术栈
- **后端框架**: FastAPI + Uvicorn
- **AI模型调用**: OpenAI SDK (支持Deepseek等兼容服务)
- **向量检索**: FAISS + NumPy
- **文本处理**: spaCy + transformers
- **图像处理**: Pillow + CLIP
- **音频处理**: GPT-SoVITS + MusicGen
- **配置管理**: JSON配置文件 + ConfigManager
- **本地模型**: Qwen3-Embedding、CLIP、Stable Diffusion

### 数据流向
1. **素材输入** → MaterialPreprocessor → temp/preprocess/
2. **预处理数据** → RAGRetrieval → data/embedding/
3. **主题输入** → 检索增强 → AI生成器群
4. **生成内容** → RenpyIntegrator → output/renpy_game/

## 4. 开发框架 (Development Framework)

### Python环境要求
- **Python版本**: 3.10+ (推荐3.12)
- **操作系统**: Windows/Linux/macOS
- **硬件要求**: 
  - 内存: 8GB+ (推荐16GB)
  - 显卡: GPU 显存大于等于 8G (用于本地AI生成)
  - 存储: 至少10GB可用空间

### 项目目录结构
```
遗游记/
├── src/                          # 核心源码
│   ├── config/                   # 配置管理
│   │   ├── paths.py             # 路径常量定义
│   │   ├── ConfigManager.py     # 配置管理器
│   │   ├── config_generators.json # 生成器配置
│   │   ├── config_rag.json      # RAG系统配置
│   │   ├── config_pipeline.json # 流程配置
│   │   ├── config_preprocessor.json # 预处理配置
│   │   └── config_api.json      # API配置
│   ├── ai_workflow/             # AI工作流
│   │   ├── preprocess/          # 素材预处理
│   │   │   ├── material_preprocessor.py
│   │   │   ├── text_preprocessor/
│   │   │   └── image_preprocess/
│   │   ├── rag/                 # RAG检索系统
│   │   │   ├── text/            # 文本RAG
│   │   │   ├── image/           # 图像RAG
│   │   │   └── rag_retrieval.py # 统一检索接口
│   │   ├── generators/          # AI生成器群
│   │   │   ├── game_framework_generator.py
│   │   │   ├── node_narrative_generator.py
│   │   │   ├── image_generator.py
│   │   │   ├── vocal_generator.py
│   │   │   ├── music_generator.py
│   │   │   ├── renpy_integrator.py
│   │   │   ├── ui_design_module.py
│   │   │   └── prompts/         # 提示词库
│   │   ├── models/              # 本地模型
│   │   ├── utils/               # 工具函数
│   │   └── main_pipeline_controller.py # 主控制器
│   ├── backend/                 # FastAPI后端服务
│   │   ├── server.py            # 服务器主程序
│   │   ├── router.py            # 路由定义
│   │   ├── service_registry.py  # 服务注册机制
│   │   └── game_services.py     # 游戏生成服务
│   └── cli_launcher/            # CLI启动器
├── data/                        # 数据存储
│   ├── embedding/               # 向量数据
│   │   ├── text/                # 文本向量(.npz格式)
│   │   └── image/               # 图像向量(FAISS索引)
│   └── retriever/               # 检索结果
├── temp/                        # 临时文件
│   ├── preprocess/              # 预处理结果
│   ├── framework/               # 框架生成结果
│   └── node/                    # 节点生成结果
├── input/                       # 输入素材
├── output/                      # 输出产物
├── config.ini                   # 主配置文件(API密钥等)
├── launcher.py                  # CLI启动器
├── start_backend.py             # 后端服务启动器
└── requirements.txt             # 依赖清单
```

### 配置管理系统
系统采用分层配置管理：
- **config.ini**: 主配置文件，包含API密钥等敏感信息
- **config_generators.json**: 生成器参数配置
- **config_rag.json**: RAG系统配置
- **config_pipeline.json**: 流程控制配置
- **config_preprocessor.json**: 预处理配置
- **config_api.json**: API服务配置
- **paths.py**: 路径常量定义
- **ConfigManager.py**: 统一配置管理器

## 5. 数据库结构 (Database Structure)

### 向量数据库设计 (FAISS索引)

#### 文本向量存储
```
data/embedding/text/
├── all_chunks.npz                    # 文本块向量
├── all_chunks_metadata.json         # 块元数据
├── all_segments_summary.npz         # 摘要向量
└── all_segments_summary_metadata.json # 摘要元数据
```

#### 图像向量存储
```
data/embedding/image/
├── faiss_index.index            # FAISS索引文件
├── image_paths.json             # 图像路径映射
└── clip_embeddings.npy          # CLIP向量数据
```

### 元数据结构详解

#### 文本Chunks元数据
```json
{
  "source_name": ""石狗见嫜流眼泪"",
  "chunk_id": ""石狗见嫜流眼泪"_chunk_0",
  "chunk": "实际的文本内容...",
  "segment_index": 1,
  "chunk_index": 1,
  "type": "chunk",
  "text_length": 868,
  "original_index": 0,
  "global_index": 0
}
```

#### 文本Summaries元数据
```json
{
  "source_name": "雷州石狗文化.txt",
  "summary_id": "summary_001",
  "use_summary": {
    "summary": "文档摘要内容...",
    "key_points": ["要点1", "要点2"],
    "themes": ["主题1", "主题2"]
  },
  "segment_count": 5,
  "total_length": 2048,
  "global_index": 0
}
```

#### 图像索引元数据
```json
{
  "image_paths": [
    "/path/to/image1.jpg",
    "/path/to/image2.png"
  ],
  "image_descriptions": [
    "石狗雕像的正面视图",
    "雷州传统建筑风格"
  ],
  "clip_model": "clip-vit-base-patch16",
  "index_created": "2024-01-01T12:00:00"
}
```

### 配置文件结构
- **config.ini**: INI格式，API密钥和基础配置
- **config_generators.json**: 生成器详细参数
- **config_rag.json**: RAG系统配置
- **config_pipeline.json**: 流程控制配置
- **config_preprocessor.json**: 预处理配置
- **config_api.json**: API服务配置

## 6. 核心模块详解 (Core Modules)

### 素材预处理模块 (MaterialPreprocessor)
**位置**: `src/ai_workflow/preprocess/material_preprocessor.py`
**功能**: 自动分类和处理多种格式的文化素材，为后续RAG检索和AI生成提供结构化数据

#### 架构设计
MaterialPreprocessor采用**分层处理架构**，包含三个核心工具类：

##### 1. Parser (文件解析器)
**位置**: `src/ai_workflow/preprocess/text_preprocessor/tools/parser.py`
**功能**: 统一的多格式文件解析接口

**支持格式**:
- **PDF文件**: 使用pdfplumber库，支持表格和图像提取
- **Word文档**: 支持.docx和.doc格式，保留文档结构信息
- **文本文件**: 支持多种编码格式自动检测

**核心方法**:
```python
def parse(self, file_path: str) -> Dict[str, Any]:
    # 返回格式:
    {
        "text": "解析后的文本内容",
        "metadata": {
            "file_type": "pdf|docx|txt",
            "file_name": "文件名",
            "pages": 页数,
            "headings": [{"text": "标题", "level": 1}],  # 文档结构
            "encoding": "utf-8"
        }
    }
```

**特性**:
- **编码检测**: 支持UTF-8、GBK、GB2312等多种编码格式
- **结构保留**: 提取文档标题层级信息，用于后续文本切分
- **错误恢复**: 解析失败时提供降级处理方案

##### 2. Splitter (文本切分器)
**位置**: `src/ai_workflow/preprocess/text_preprocessor/tools/splitter.py`
**功能**: 多粒度文本切分，支持语义感知的文本分段

**切分策略**:
- **章节切分 (Segments)**: 基于文档结构或模式匹配
  - 支持多种标题模式: "第X章"、"Chapter X"、Markdown标题等
  - 利用Parser提取的标题信息进行结构化切分
- **细粒度切分 (Chunks)**: 固定长度切分，支持重叠
  - 默认chunk_size: 1500字符
  - 默认overlap: 100字符，保持语义连续性

**核心方法**:
```python
def split_to_chapters(self, text: str, source_name: str,
                     use_headings: Optional[List[Dict]] = None) -> List[Dict]:
    # 返回segments列表，每个segment包含:
    {
        "text": "章节文本内容",
        "title": "章节标题",
        "index": 1,
        "source_name": "源文件名",
        "file_path": "保存路径"
    }

def split_to_chunks(self, segments: List[Dict], chunk_size: int = 1500,
                   overlap: int = 100) -> List[Dict]:
    # 返回chunks列表，支持跨segment的连续切分
```

**核心特性**:
- **语义边界检测**: 优先在句号、段落等自然边界切分
- **重叠策略**: 避免重要信息被切分边界丢失
- **元数据保留**: 维护chunk与原始segment的映射关系

##### 3. Summarizer (摘要生成器)
**位置**: `src/ai_workflow/preprocess/text_preprocessor/tools/summarizer.py`
**功能**: 生成结构化文档摘要(SAD - Structured Abstract Document)

**摘要类型**:
- **文档级摘要**: 整个文档的主要内容概括
- **章节级摘要**: 每个segment的要点提取
- **主题提取**: 识别文档中的关键主题和概念

**生成策略**:
```python
def generate_summary(self, text: str, max_length: int = 300) -> Dict:
    # 使用LLM生成结构化摘要
    {
        "summary": "文档摘要内容",
        "key_points": ["要点1", "要点2", "要点3"],
        "themes": ["主题1", "主题2"],
        "entities": ["实体1", "实体2"],  # 命名实体识别
        "length": 原文长度,
        "compression_ratio": 压缩比
    }
```

#### 完整处理流程

**第一阶段: 文件分类与解析**
```python
# 1. 文件分类
text_files, image_files = preprocessor.classify_files(material_paths)

# 2. 并行解析处理
for text_file in text_files:
    parse_result = parser.parse(text_file)  # 提取文本和结构信息
```

**第二阶段: 多粒度切分**
```python
# 3. 章节切分 (基于文档结构)
segments = splitter.split_to_chapters(
    text=parse_result['text'],
    source_name=file_stem,
    use_headings=parse_result['metadata'].get('headings')
)

# 4. 细粒度切分 (固定长度+重叠)
chunks = splitter.split_to_chunks(segments, chunk_size=1500, overlap=100)
```

**第三阶段: 摘要生成**
```python
# 5. 生成多级摘要
document_summary = summarizer.generate_summary(full_text)
segment_summaries = [summarizer.generate_summary(seg['text']) for seg in segments]
```

**输出数据结构**:
```json
{
  "source_name": "雷州石狗文化",
  "segments": [
    {
      "segment_id": "seg_1",
      "text": "章节内容...",
      "title": "石狗的历史渊源",
      "summary": "本章节介绍了..."
    }
  ],
  "chunks": [
    {
      "chunk_id": "chunk_1",
      "text": "细分内容...",
      "segment_index": 1,
      "chunk_index": 1
    }
  ],
  "document_summary": {
    "summary": "整体摘要...",
    "key_points": ["要点1", "要点2"],
    "themes": ["石狗文化", "民间信仰"]
  }
}
```

#### 性能特性
- **并行处理**: 多文件同时处理，加快处理速度
- **增量更新**: 支持新增文件的增量处理
- **内存管理**: 大文件分块处理，避免内存溢出
- **缓存机制**: 解析结果缓存，避免重复处理

### RAG检索增强系统 (RAGRetrieval)
**位置**: `src/ai_workflow/rag/rag_retrieval.py`
**功能**: 统一管理文本和图像RAG功能，提供检索服务

#### 双模态RAG架构

##### 1. 文本RAG子系统 (`src/ai_workflow/rag/text/`)

**EmbeddingTool (向量化工具)**
**位置**: `src/ai_workflow/rag/text/embedding_tool.py`
**功能**: 支持本地模型和云API的统一向量化接口

**核心特性**:
- **双模式支持**: 本地Qwen3-Embedding-0.6B模型 + 云端API
- **降级机制**: 本地模型加载失败时切换到API服务
- **批量处理**: 支持大规模文本的批量向量化
- **缓存机制**: 避免重复计算相同文本的向量

**关键方法**:
```python
def embed_texts(self, texts: List[str], batch_size: int = 32) -> np.ndarray:
    # 批量向量化，返回shape为(n_texts, embedding_dim)的数组

def embed_single_text(self, text: str) -> np.ndarray:
    # 单文本向量化，返回1D向量数组

def save_embeddings(self, embeddings: np.ndarray, metadata: List[Dict],
                   filename: str) -> str:
    # 保存向量和元数据到.npz和.json文件
```

**Retriever (检索器)**
**位置**: `src/ai_workflow/rag/text/retriever.py`
**功能**: 基于FAISS的相似度检索

**检索算法**:
- **余弦相似度**: 默认相似度计算方法
- **FAISS检索**: 使用FAISS库进行向量检索
- **阈值过滤**: 可配置的相似度阈值，过滤低质量结果

**核心方法**:
```python
def search(self, query_embedding: np.ndarray, top_k: int = 5,
          threshold: float = 0.7) -> List[Dict]:
    # 返回格式:
    [
        {
            "content": "检索到的文本内容",
            "similarity": 0.85,
            "metadata": {"source": "文件名", "chunk_id": "chunk_1"},
            "index": 0
        }
    ]
```

**TextRAG (文本RAG统一接口)**
**位置**: `src/ai_workflow/rag/text/text_rag.py`
**功能**: 提供统一的文本检索接口，支持多种粒度

**检索粒度**:
- **summaries**: 文档级摘要检索，适合获取整体概念
- **segments**: 章节级检索，适合获取结构化内容
- **chunks**: 细粒度检索，适合获取具体细节

**使用示例**:
```python
# 检索相关摘要
summaries = text_rag.search("石狗文化", search_type="summaries", top_k=3)

# 检索相关章节
segments = text_rag.search("雷州历史", search_type="segments", top_k=5)

# 检索具体细节
chunks = text_rag.search("石狗制作工艺", search_type="chunks", top_k=10)
```

##### 2. 图像RAG子系统 (`src/ai_workflow/rag/image/`)

**CLIPImageIndexer (图像索引器)**
**位置**: `src/ai_workflow/rag/image/clip_image_indexer.py`
**功能**: 使用CLIP进行图像和文本的跨模态embedding

**核心功能**:
- **图像编码**: 将图像转换为CLIP向量表示
- **文本编码**: 将文本描述转换为与图像对齐的向量空间
- **跨模态检索**: 支持文本查询图像、图像查询相似图像

**TextImageRetriever (文本到图像检索)**
**位置**: `src/ai_workflow/rag/image/text_image_retriever.py`
**功能**: 基于文本描述检索相关图像

**检索流程**:
```python
def retrieve_images(self, text_query: str, top_k: int = 5) -> List[Dict]:
    # 1. 文本编码为CLIP向量
    text_embedding = self.clip_model.encode_text(text_query)

    # 2. 在图像向量库中检索
    similar_images = self.search_similar_images(text_embedding, top_k)

    # 3. 返回图像路径和相似度
    return [
        {
            "image_path": "/path/to/image.jpg",
            "similarity": 0.78,
            "description": "图像描述"
        }
    ]
```

**ImageRAG (图像RAG增强)**
**位置**: `src/ai_workflow/rag/image/image_rag.py`
**功能**: 为图像生成提供参考图像检索

**增强策略**:
- **语义匹配**: 根据生成需求检索语义相关的参考图像
- **风格一致性**: 检索具有相似视觉风格的图像
- **IP-Adapter集成**: 将检索到的图像作为IP-Adapter的输入

#### 统一检索接口 (RAGRetrieval)

**核心方法**:
```python
def search_text(self, search_type: str, query: str, top_k: int = 5) -> List[Dict]:
    # 统一的文本检索接口

def search_images(self, query: str, top_k: int = 5) -> List[Dict]:
    # 图像检索接口

def build_index(self) -> Dict:
    # 构建所有索引（文本+图像）

def get_enhanced_context(self, query: str, include_images: bool = True) -> Dict:
    # 获取增强上下文（文本+图像）
```

#### 性能特性
- **懒加载**: 模型和索引按需加载，节省内存
- **批量处理**: 支持大规模数据的批量索引构建
- **增量更新**: 支持新增数据的增量索引更新
- **缓存策略**: 查询结果缓存，加快重复查询速度

### AI生成器模块群
**位置**: `src/ai_workflow/generators/`

#### 1. GameFrameworkGenerator (Phase I - 故事骨架生成)
**文件**: `game_framework_generator.py`
**功能**: 基于RAG检索生成游戏主线剧情骨架和角色设定

##### 核心架构
**三阶段生成流程**:
1. **RAG检索阶段**: 检索相关文化背景和主题内容
2. **框架生成阶段**: 使用Deepseek-R1生成结构化剧情骨架
3. **角色生成阶段**: 基于剧情需求生成详细角色设定

**RAG增强策略**:
```python
def _get_rag_context(self, theme: str, reference_materials: List[str]) -> str:
    # 1. 检索文档摘要 (SAD)
    summaries = self.rag.search_text("summaries", theme, top_k=3)

    # 2. 检索主题相关chunks
    chunks = self.rag.search_text("chunks", theme, top_k=5)

    # 3. 检索参考材料相关内容
    ref_content = []
    for ref in reference_materials:
        ref_chunks = self.rag.search_text("chunks", ref, top_k=3)
        ref_content.extend(ref_chunks)

    return self._format_context(summaries, chunks, ref_content)
```

**提示词工程**:
**位置**: `src/ai_workflow/generators/prompts/framework_prompts.py`
- **系统提示词**: 定义视觉小说剧情架构师角色
- **结构化输出**: 强制JSON格式输出，包含mainline_nodes、sideline_nodes、node_relationships
- **文化一致性**: 要求引用检索到的资料，保持剧情与文化内涵一致

**输出数据结构**:
```json
{
  "mainline_nodes": [
    {
      "node_id": "A1",
      "description": "开场：介绍雷州石狗文化背景",
      "key_events": ["主角初到雷州", "遇见石狗传说"],
      "characters": ["主角", "当地向导"]
    }
  ],
  "sideline_nodes": [
    {
      "node_id": "B1",
      "description": "支线：探索石狗制作工艺",
      "key_events": ["拜访石狗工匠"],
      "characters": ["老工匠"]
    }
  ],
  "node_relationships": [
    {"from": "A1", "to": "A2", "relationship": "顺序"},
    {"from": "A2", "to": "B1", "relationship": "分支"}
  ],
  "characters": [
    {
      "name": "林小满",
      "role": "主角",
      "background": "文化研究者",
      "personality": "好奇、执着、善良"
    }
  ]
}
```

#### 2. NodeNarrativeGenerator (Phase II - 节点叙事生成)
**文件**: `node_narrative_generator.py`
**功能**: 为每个剧情节点生成具体的叙事内容

##### 生成策略
**上下文感知机制**:
- **前文情节**: 维护剧情发展的连贯性
- **角色关系**: 基于角色设定生成符合性格的对话
- **情感状态**: 跟踪角色情感变化，影响对话风格

**RAG动态检索**:
```python
def _get_node_context(self, node_info: Dict, previous_nodes: List[Dict]) -> str:
    # 1. 基于节点关键事件检索相关细节
    events = node_info.get("key_events", [])
    event_context = []
    for event in events:
        chunks = self.rag.search_text("chunks", event, top_k=3)
        event_context.extend(chunks)

    # 2. 检索角色相关背景
    characters = node_info.get("characters", [])
    char_context = self._get_character_context(characters)

    return self._format_narrative_context(event_context, char_context)
```

**多元素生成**:
- **对话系统**: 角色间的自然对话，包含情感标记
- **场景描述**: 详细的环境和氛围描述
- **选择分支**: 玩家互动选项和分支逻辑
- **特效标记**: 音效、视效等游戏元素标记

**输出格式**:
```json
{
  "node_id": "A1",
  "narrative_content": {
    "settings": ["雷州古镇街道", "黄昏时分"],
    "dialogues": [
      {
        "character": "narrator",
        "content": "夕阳西下，古老的雷州街道显得格外宁静...",
        "emotion": "平静"
      },
      {
        "character": "林小满",
        "content": "这些石狗雕像真是栩栩如生啊！",
        "emotion": "惊叹"
      }
    ],
    "choices": [
      {
        "text": "仔细观察石狗的细节",
        "next_node": "A2a"
      },
      {
        "text": "询问当地人石狗的来历",
        "next_node": "A2b"
      }
    ],
    "effects": [
      {"type": "music", "value": "古镇黄昏.mp3"},
      {"type": "sound", "value": "脚步声.wav"}
    ]
  }
}
```

#### 3. ImageGenerator (RAG增强图像生成)
**文件**: `image_generator.py` + `rag/image/enhanced_image_generator.py`
**功能**: 生成背景、立绘、CG等视觉资源

##### RAG增强机制
**参考图像检索**:
```python
def _get_reference_images(self, description: str, image_type: str) -> List[str]:
    # 1. 基于描述检索相似图像
    similar_images = self.image_rag.retrieve_images(description, top_k=3)

    # 2. 根据图像类型过滤
    filtered_images = self._filter_by_type(similar_images, image_type)

    # 3. 返回参考图像路径
    return [img["image_path"] for img in filtered_images]
```

**IP-Adapter集成**:
- **风格一致性**: 使用参考图像指导生成，保持视觉风格统一
- **文化一致性**: 基于真实文化图像生成，保持文化元素一致
- **质量控制**: 多轮生成和筛选，提高图像质量

**多模式支持**:
- **background**: 960x540分辨率，用于场景背景
- **character**: 512x768分辨率，用于角色立绘
- **cg**: 1024x768分辨率，用于CG插图
- **ui**: 自定义尺寸，用于UI元素

**提示词工程**:
```python
PROMPT_TEMPLATES = {
    "background": "masterpiece, wallpaper, 8k, detailed CG, {prompt}, (no_human)",
    "character": "masterpiece, wallpaper, 8k, detailed CG, {prompt}, (upper_body), solo",
    "cg": "masterpiece, wallpaper, 8k, detailed CG, {prompt}, cinematic lighting"
}
```

#### 4. VocalGenerator (语音合成)
**文件**: `vocal_generator.py`
**功能**: 基于GPT-SoVITS的角色配音生成

##### 核心特性
**多音色支持**:
```python
# voice_config.json配置示例
{
    "character_voice_mapping": {
        "narrator": "006",      # 旁白音色
        "林小满": "002",        # 女主角音色
        "祖母": "005",          # 老年女性音色
        "工匠": "003"           # 男性角色音色
    },
    "default_voice": "001",
    "audio_format": "wav",
    "sample_rate": 22050
}
```

**语音生成流程**:
```python
def generate_voice(self, text: str, character: str, emotion: str = "neutral") -> str:
    # 1. 角色音色映射
    voice_id = self.voice_mapping.get(character, self.default_voice)

    # 2. 情感调节
    adjusted_text = self._apply_emotion(text, emotion)

    # 3. 调用GPT-SoVITS API
    audio_path = self._call_sovits_api(adjusted_text, voice_id)

    return audio_path
```

**批量处理特性**:
- **并行生成**: 多个音频文件同时生成
- **缓存机制**: 相同文本和音色的结果缓存复用
- **质量控制**: 检测生成质量，低质量时重新生成

#### 5. MusicGenerator (背景音乐生成)
**文件**: `music_generator.py`
**功能**: 基于MusicGen的情感驱动背景音乐生成

##### 情感驱动生成
**情感分类系统**:
```python
EMOTION_TEMPLATES = {
    "peaceful": "peaceful, calm, traditional Chinese music, guqin, soft",
    "mysterious": "mysterious, ancient, traditional instruments, atmospheric",
    "epic": "epic, grand, traditional Chinese orchestra, dramatic",
    "sad": "melancholic, emotional, traditional Chinese flute, slow tempo",
    "joyful": "happy, uplifting, traditional celebration music, festive"
}
```

**生成参数控制**:
```python
def generate_music(self, emotion: str, duration: int = 30,
                  style: str = "traditional_chinese") -> str:
    # 1. 构建提示词
    prompt = self._build_music_prompt(emotion, style)

    # 2. 调用MusicGen API
    audio_data = self._call_musicgen_api(
        prompt=prompt,
        duration=duration,
        sample_rate=32000
    )

    # 3. 后处理和保存
    return self._save_audio(audio_data, f"{emotion}_{duration}s.wav")
```

#### 6. UIDesignModule (UI设计模块)
**文件**: `ui_design_module.py`
**功能**: 文化遗产主题的UI设计生成

##### 主题化设计
**色彩方案**:
```python
CULTURAL_THEMES = {
    "leizhoudog": {
        "primary_color": "#8B4513",      # 石狗棕色
        "secondary_color": "#DAA520",    # 金黄色
        "background_color": "#F5F5DC",   # 米色背景
        "text_color": "#2F4F4F"          # 深灰色文字
    }
}
```

**UI元素生成**:
- **对话框**: 文化主题装饰的对话框样式
- **按钮**: 传统文化元素的按钮设计
- **背景**: 文化符号装饰的界面背景
- **字体**: 适合文化主题的字体选择

#### 7. RenpyIntegrator (Ren'Py脚本整合器)
**文件**: `renpy_integrator.py`
**功能**: 将所有生成内容整合为完整的Ren'Py游戏项目

##### 脚本生成架构
**完整整合流程**:
```python
def integrate_full_game(self, framework: Dict, narratives: List[Dict],
                       image_results: List[Dict], audio_results: List[Dict]) -> str:
    # 1. 生成角色定义
    character_definitions = self._generate_character_definitions(framework)

    # 2. 生成主脚本内容
    main_script = self._generate_main_script(narratives, image_results, audio_results)

    # 3. 生成配置文件
    options_file = self._generate_options_file(framework)

    # 4. 整合资源文件
    self._organize_resources(image_results, audio_results)

    return script_path
```

**脚本结构**:
```python
# 生成的script.rpy结构
"""
# 角色定义
define narrator = Character("旁白")
define xiaoman = Character("林小满")

# 游戏开始
label start:
    # 节点A1
    scene background_ancient_town
    play music "peaceful_30s.wav"
    narrator "夕阳西下，古老的雷州街道显得格外宁静..."
    show xiaoman happy
    xiaoman "这些石狗雕像真是栩栩如生啊！"

    menu:
        "仔细观察石狗的细节":
            jump node_a2a
        "询问当地人石狗的来历":
            jump node_a2b

    return
"""
```

**资源管理**:
- **图像资源**: 复制到images/目录，按类型分类
- **音频资源**: 复制到audio/目录，包含音乐和音效
- **脚本文件**: 生成主脚本和选项配置文件
- **项目结构**: 创建标准的Ren'Py项目目录结构

### 主流程控制器 (MainPipelineController)
**位置**: `src/ai_workflow/main_pipeline_controller.py`
**功能**: 统一编排所有模块，实现端到端流程控制

#### 核心架构设计

##### 1. 懒加载模块管理
**设计理念**: 使用@property装饰器实现模块的按需初始化，减少内存使用

```python
@property
def material_preprocessor(self):
    if self._material_preprocessor is None:
        from src.ai_workflow.preprocess.material_preprocessor import MaterialPreprocessor
        self._material_preprocessor = MaterialPreprocessor()
    return self._material_preprocessor

@property
def rag_retrieval(self):
    if self._rag_retrieval is None:
        from src.ai_workflow.rag.rag_retrieval import RAGRetrieval
        self._rag_retrieval = RAGRetrieval()
    return self._rag_retrieval
```

**资源清理机制**:
```python
def cleanup_module(self, module_name: str):
    """清理指定模块，释放内存和GPU资源"""
    if hasattr(self, f"_{module_name}") and getattr(self, f"_{module_name}") is not None:
        # 删除模块实例
        delattr(self, f"_{module_name}")
        setattr(self, f"_{module_name}", None)

        # 强制垃圾回收
        import gc
        gc.collect()

        # 清理CUDA缓存
        try:
            import torch
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
        except ImportError:
            pass
```

##### 2. 流程状态管理
**状态跟踪系统**:
```python
def __init__(self):
    self.pipeline_state = {
        "current_step": 0,
        "total_steps": 9,
        "step_names": [
            "素材预处理", "RAG索引构建", "游戏框架生成",
            "节点叙事生成", "图像生成", "音频生成",
            "Ren'Py脚本整合", "UI设计生成", "最终输出"
        ],
        "step_times": {},
        "start_time": None,
        "errors": []
    }
```

**步骤管理方法**:
```python
def _start_step(self, step_name: str):
    """开始执行步骤"""
    self.pipeline_state["current_step"] += 1
    current_step = self.pipeline_state["current_step"]
    total_steps = self.pipeline_state["total_steps"]

    print(f"\n[{current_step}/{total_steps}] {step_name}")
    print("-" * 50)

    self.pipeline_state["step_times"][step_name] = {"start": time.time()}

def _complete_step(self):
    """完成当前步骤"""
    current_step = self.pipeline_state["current_step"]
    step_name = self.pipeline_state["step_names"][current_step - 1]

    end_time = time.time()
    start_time = self.pipeline_state["step_times"][step_name]["start"]
    duration = end_time - start_time

    self.pipeline_state["step_times"][step_name]["end"] = end_time
    self.pipeline_state["step_times"][step_name]["duration"] = duration

    print(f"✓ {step_name} 完成 (耗时: {duration:.2f}秒)")
```

#### 完整流程详解

##### 主入口方法
```python
def generate_complete_game(self, material_paths: List[str], theme: str = None,
                          keywords: List[str] = None) -> Dict:
    """
    完整的游戏生成流程
    从素材输入到游戏输出的一站式处理
    """
    print("=" * 60)
    print("开始文化遗产严肃游戏生成流程")
    print("=" * 60)

    self.pipeline_state["start_time"] = time.time()
    results = {}

    try:
        # 执行9个步骤
        results["preprocessing"] = self._step_1_material_preprocessing(material_paths)
        results["rag_indexing"] = self._step_2_rag_indexing()
        results["framework"] = self._step_3_framework_generation(theme, keywords)
        results["narratives"] = self._step_4_narrative_generation(results["framework"])
        results["images"] = self._step_5_image_generation(results["narratives"])
        results["audio"] = self._step_6_audio_generation(results["narratives"])
        results["renpy_script"] = self._step_7_renpy_integration(...)
        results["ui_system"] = self._step_8_ui_generation(...)
        results["final_output"] = self._step_9_final_output(results)

        # 生成完成报告
        completion_report = self._generate_completion_report(results)
        results["completion_report"] = completion_report

        return results

    except Exception as e:
        self._save_error_report(e, results)
        raise
```

##### 各步骤详细实现

**步骤1: 素材预处理**
```python
def _step_1_material_preprocessing(self, material_paths: List[str]) -> Dict:
    self._start_step("素材预处理")

    print("处理输入素材...")
    preprocessing_results = self.material_preprocessor.process_materials(material_paths)

    print(f"生成SAD文件: {len(preprocessing_results['text_files'])}")
    print(f"生成VSI文件: {len(preprocessing_results['image_files'])}")

    # 清理预处理器，释放资源
    self.cleanup_module("material_preprocessor")

    self._complete_step()
    return preprocessing_results
```

**步骤2: RAG索引构建**
```python
def _step_2_rag_indexing(self) -> Dict:
    self._start_step("RAG索引构建")

    print("构建文本向量索引...")
    text_index_result = self.rag_retrieval.build_text_index()

    print("构建图像向量索引...")
    image_index_result = self.rag_retrieval.build_image_index()

    indexing_results = {
        "text_index": text_index_result,
        "image_index": image_index_result
    }

    self._complete_step()
    return indexing_results
```

**步骤3: 游戏框架生成**
```python
def _step_3_framework_generation(self, theme: str, keywords: List[str]) -> Dict:
    self._start_step("游戏框架生成")

    print(f"生成主题: {theme}")
    framework_result = self.framework_generator.generate_story_framework(
        theme=theme,
        reference_materials=keywords or []
    )

    # 清理框架生成器
    self.cleanup_module("framework_generator")

    self._complete_step()
    return framework_result
```

#### 错误处理和恢复机制

**错误报告生成**:
```python
def _save_error_report(self, error: Exception, partial_results: Dict):
    """保存错误报告，用于调试和恢复"""
    error_report = {
        "error_type": type(error).__name__,
        "error_message": str(error),
        "current_step": self.pipeline_state["current_step"],
        "completed_steps": list(partial_results.keys()),
        "step_times": self.pipeline_state["step_times"],
        "timestamp": time.time()
    }

    # 保存到文件
    error_file = os.path.join("temp", "error_reports", f"error_{int(time.time())}.json")
    os.makedirs(os.path.dirname(error_file), exist_ok=True)

    with open(error_file, 'w', encoding='utf-8') as f:
        json.dump(error_report, f, ensure_ascii=False, indent=2)
```

**断点续传支持**:
```python
def resume_from_checkpoint(self, checkpoint_file: str) -> Dict:
    """从检查点恢复执行"""
    with open(checkpoint_file, 'r', encoding='utf-8') as f:
        checkpoint_data = json.load(f)

    # 恢复状态
    self.pipeline_state = checkpoint_data["pipeline_state"]
    partial_results = checkpoint_data["results"]

    # 从中断点继续执行
    current_step = self.pipeline_state["current_step"]
    # ... 继续执行剩余步骤
```

#### 性能监控和报告

**完成报告生成**:
```python
def _generate_completion_report(self, results: Dict) -> Dict:
    """生成详细的完成报告"""
    total_time = time.time() - self.pipeline_state["start_time"]

    # 统计各步骤耗时
    step_statistics = {}
    for step_name, times in self.pipeline_state["step_times"].items():
        step_statistics[step_name] = {
            "duration": times.get("duration", 0),
            "percentage": (times.get("duration", 0) / total_time) * 100
        }

    # 统计生成内容
    content_statistics = {
        "total_nodes": len(results.get("narratives", [])),
        "total_images": len(results.get("images", [])),
        "total_audio_files": len(results.get("audio", [])),
        "script_lines": self._count_script_lines(results.get("renpy_script", {}))
    }

    return {
        "total_time": total_time,
        "step_statistics": step_statistics,
        "content_statistics": content_statistics,
        "success": True,
        "timestamp": time.time()
    }
```

## 7. API接口说明 (API Documentation)

### 后端服务架构
**位置**: `src/backend/`
基于FastAPI的微服务架构，提供RESTful API接口。

#### 核心接口列表

| 接口路径 | 方法 | 功能描述 | 调用方式 |
|---------|------|----------|----------|
| `/api/v1/call` | POST | 同步服务调用 | 立即返回结果 |
| `/api/v1/call-async` | POST | 异步服务调用 | 返回任务ID |
| `/api/v1/task/{task_id}` | GET | 查询任务状态 | 轮询检查 |
| `/api/v1/services` | GET | 获取服务列表 | 服务发现 |
| `/health` | GET | 健康检查 | 服务状态 |

#### 可用服务列表

| 服务名称 | 功能描述 | 主要参数 | 执行时间 |
|---------|----------|----------|----------|
| `generate_complete_game` | 生成完整的文化遗产游戏 | theme, material_paths, reference_materials | 5-30分钟 |
| `test_material_preprocessing` | 测试素材预处理功能 | test_text(可选) | 10-30秒 |
| `test_rag_retrieval` | 测试RAG检索功能 | query(可选), top_k(可选) | 1-5秒 |
| `run_system_test` | 运行完整系统测试 | 无 | 30-60秒 |
| `ping` | 服务连通性测试 | 无 | <1秒 |
| `get_system_info` | 获取系统基本信息 | 无 | <1秒 |
| `check_configuration` | 检查配置状态 | 无 | 1-3秒 |
| `list_services` | 获取所有可用服务 | 无 | <1秒 |
| `service_info` | 获取特定服务信息 | service_name | <1秒 |

#### 服务注册机制
```python
@register_service(
    "generate_complete_game",
    "生成完整的文化遗产严肃游戏",
    {
        "theme": {"type": "str", "required": True},
        "material_paths": {"type": "List[str]", "required": True}
    }
)
def generate_complete_game_service(theme: str, material_paths: List[str]):
    return _game_services.generate_complete_game(theme, material_paths)
```

#### 响应格式
```json
{
  "status": "success|error",
  "message": "响应消息",
  "data": "具体数据",
  "timestamp": "2024-01-01T12:00:00"
}
```

## 8. 部署与运行 (Deployment & Operation)

### 环境配置要求
**Python版本**: 3.10+ (推荐3.12)

1. **安装Python依赖**:
   ```bash
   pip install -r requirements.txt
   pip install -r src/backend/requirements.txt
   python -m spacy download zh_core_web_sm
   ```

2. **配置API密钥**:
   编辑`src/config/config_api.json`文件，配置必要的API服务：

   ```json
   {
     "openai": {
       "api_key": "your_deepseek_api_key",
       "base_url": "https://api.deepseek.com/v1",
       "model": "deepseek-reasoner"
     },
     "default_provider": "openai"
   }
   ```

   **可选配置** - 编辑`src/config/config_preprocessor.json`添加其他服务密钥：
   ```json
   {
     "OPENAI_API_KEY": "your_deepseek_api_key",
     "OPENAI_BASE_URL": "https://api.deepseek.com/v1",
     "OPENAI_MODEL": "deepseek-reasoner"
   }
   ```

   **注意**:
   - 主要配置在`config_api.json`中管理
   - 图像生成、语音合成等功能的API密钥根据需要在对应配置文件中设置
   - 支持多个API提供商，可配置故障转移机制

### 启动方式
```bash
# 方式1: CLI界面启动
python launcher.py

# 方式2: API服务启动
python start_backend.py

# 方式3: 直接运行测试
python src/backend/test_backend.py
```

### 配置文件详解

遗游记采用**分层配置管理**设计，通过统一的ConfigManager实现全局配置管理，支持嵌套键访问、动态加载和热更新。

#### 配置系统架构

**核心设计理念**：
- **模块化配置**: 每个功能模块独立配置文件，避免配置冲突
- **统一管理**: 通过ConfigManager提供统一的配置访问接口
- **嵌套键支持**: 支持`image_generation.use_cloud`形式的嵌套访问
- **动态加载**: 配置文件按需加载，支持运行时重新加载
- **类型安全**: 提供默认值机制，提高配置访问的安全性

#### 配置文件结构

```
src/config/
├── ConfigManager.py          # 配置管理器核心实现
├── __init__.py              # 统一配置接口导出
├── paths.py                 # 路径常量定义
├── config_api.json          # API服务配置
├── config_generators.json   # 生成器模块配置
├── config_pipeline.json     # 流程控制配置
├── config_preprocessor.json # 预处理模块配置
└── config_rag.json         # RAG系统配置
```

#### 配置管理器 (ConfigManager)

#### 核心API函数

##### get_config(module, key, default=None)
获取指定模块的配置值，支持嵌套键访问。

**参数**：
- `module` (str): 配置模块名称，对应config_{module}.json文件
- `key` (str): 配置键名，支持点号分隔的嵌套键（如'openai.api_key'）
- `default` (Any, 可选): 配置不存在时的默认值

**返回值**：
- `Any`: 配置值，如果不存在则返回default值

**示例**：
```python
from src.config import get_config

# 获取API密钥
api_key = get_config('api', 'openai.api_key', default='')

# 获取嵌套配置
temperature = get_config('generators', 'framework_generation.temperature', default=0.7)

# 获取数组配置
providers = get_config('api', 'fallback_providers', default=[])
```

##### set_config(module, key, value)
设置指定模块的配置值并保存到文件。

**参数**：
- `module` (str): 配置模块名称
- `key` (str): 配置键名，支持嵌套键
- `value` (Any): 要设置的配置值

**返回值**：
- `None`

**示例**：
```python
from src.config import set_config

# 设置API模型
set_config('api', 'openai.model', 'deepseek-reasoner')

# 设置嵌套配置
set_config('generators', 'image_generation.default_steps', 30)

# 设置复杂对象
set_config('api', 'rate_limiting', {
    'requests_per_minute': 100,
    'enable_rate_limit': True
})
```

##### get_section(module, section)
获取配置文件中的完整配置节。

**参数**：
- `module` (str): 配置模块名称
- `section` (str): 配置节名称

**返回值**：
- `Dict[str, Any]`: 配置节的完整字典，如果不存在则返回空字典

**示例**：
```python
from src.config import get_section

# 获取OpenAI完整配置
openai_config = get_section('api', 'openai')
# 返回: {'api_key': '...', 'base_url': '...', 'model': '...', ...}

# 获取图像生成配置
image_config = get_section('generators', 'image_generation')
steps = image_config.get('default_steps', 25)
```

**特性**：
- **懒加载**: 配置文件仅在首次访问时加载
- **缓存机制**: 已加载的配置保存在内存中，加快访问速度
- **错误处理**: 文件不存在或格式错误时提供友好的错误信息
- **热重载**: 支持运行时重新加载配置文件
#### 1. config_api.json (API服务配置)
**功能**: 管理所有外部API服务的配置，支持多提供商和故障转移

```json
{
  "openai": {
    "api_key": "your_deepseek_api_key",
    "base_url": "https://api.deepseek.com/v1",
    "model": "deepseek-reasoner",
    "temperature": 0.7,
    "max_tokens": 4000,
    "timeout": 120,
    "retry_times": 3,
    "retry_delay": 1
  },
  "dashscope": {
    "api_key": "",
    "base_url": "https://dashscope.aliyuncs.com/api/v1",
    "model": "qwen-turbo"
  },
  "default_provider": "openai",
  "fallback_providers": ["dashscope", "custom"],
  "enable_fallback": true,
  "rate_limiting": {
    "requests_per_minute": 60,
    "tokens_per_minute": 100000,
    "enable_rate_limit": false
  },
  "caching": {
    "enable_cache": true,
    "cache_ttl": 3600,
    "max_cache_size": 1000
  }
}
```

**关键特性**：
- **多提供商支持**: 支持OpenAI、DashScope等多个API提供商
- **故障转移**: 主提供商失败时切换到备用提供商
- **速率限制**: 内置请求频率控制，避免API限制
- **缓存机制**: 减少重复请求，加快响应速度

#### 2. config_generators.json (生成器配置)
**功能**: 配置所有AI生成器的参数和行为

```json
{
  "image_generation": {
    "similarity_threshold": 0.2,
    "ip_adapter_scale": 0.5,
    "default_steps": 25,
    "default_guidance_scale": 7.5,
    "dimensions": {
      "background": {"width": 960, "height": 540},
      "character": {"width": 512, "height": 768}
    },
    "prompt_templates": {
      "background": "masterpiece, wallpaper, 8k, detailed CG, {prompt}, (no_human)",
      "character": "masterpiece, wallpaper, 8k, detailed CG, {prompt}, (upper_body), solo"
    }
  },
  "vocal_generation": {
    "default_voice": "001",
    "audio_format": "wav",
    "sample_rate": 22050,
    "character_voice_mapping": {
      "narrator": "006", "林小满": "002", "祖母": "005"
    }
  },
  "framework_generation": {
    "model": "deepseek-reasoner",
    "temperature": 0.8,
    "max_tokens": 6000,
    "enable_rag": true,
    "rag_top_k": 5,
    "max_nodes": 10
  },
  "narrative_generation": {
    "temperature": 0.7,
    "max_tokens": 2000,
    "enable_rag": true,
    "rag_top_k": 3
  }
}
```

#### 3. config_pipeline.json (流程控制配置)
**功能**: 管理主流程控制器的行为和性能参数

```json
{
  "workflow": {
    "total_steps": 9,
    "step_names": ["素材预处理", "RAG索引构建", "游戏框架生成", ...],
    "enable_step_validation": true,
    "continue_on_error": false,
    "save_intermediate_results": true
  },
  "output": {
    "base_directory": "output",
    "timestamp_format": "%Y%m%d_%H%M%S",
    "create_subdirectories": true,
    "cleanup_temp_files": true
  },
  "performance": {
    "max_parallel_tasks": 4,
    "memory_limit_gb": 16,
    "timeout_per_step": 1800,
    "auto_gc": true
  },
  "recovery": {
    "enable_checkpoints": true,
    "checkpoint_interval": 1,
    "auto_resume": false,
    "max_retry_attempts": 3
  }
}
```

#### 4. config_rag.json (RAG系统配置)
**功能**: 配置检索增强生成系统的所有参数

```json
{
  "embedding": {
    "model": "Qwen/Qwen3-Embedding-0.6B",
    "dimension": 1024,
    "batch_size": 32,
    "use_local": true,
    "timeout": 60
  },
  "vector_store": {
    "use_faiss": true,
    "index_type": "IVF",
    "nlist": 100,
    "similarity_metric": "cosine"
  },
  "retrieval": {
    "similarity_threshold": 0.7,
    "max_results": 10,
    "default_top_k": 5,
    "enable_rerank": false
  },
  "sad_processing": {
    "chunk_size": 1000,
    "chunk_overlap": 100,
    "enable_summary": true,
    "summary_max_length": 300
  },
  "vsi_processing": {
    "supported_formats": [".jpg", ".jpeg", ".png", ".bmp", ".gif"],
    "enable_ocr": true,
    "enable_image_description": true
  }
}
```

#### 5. config_preprocessor.json (预处理配置)
**功能**: 配置素材预处理模块的详细参数

```json
{
  "DEFAULT_CHUNK_SIZE": 1500,
  "DEFAULT_OVERLAP": 100,
  "MAX_CHUNK_SIZE": 4000,
  "MIN_CHUNK_SIZE": 100,
  "CHAPTER_PATTERNS": [
    "^第[一二三四五六七八九十\\d]+章.*",
    "^Chapter\\s+\\d+.*",
    "^##?\\s+.*"
  ],
  "SUPPORTED_EXTENSIONS": [".pdf", ".docx", ".doc", ".txt"],
  "DEFAULT_EMBEDDING_MODEL": "Qwen/Qwen3-Embedding-0.6B",
  "EMBEDDING_BATCH_SIZE": 32,
  "USE_LOCAL_EMBEDDING": true,
  "image_preprocessor": {
    "clip_model_path": "src/ai_workflow/models/clip-vit-base-patch16",
    "supported_formats": [".jpg", ".jpeg", ".png", ".bmp", ".tiff"],
    "generation_settings": {
      "default_width": 512,
      "default_height": 768,
      "num_inference_steps": 25,
      "guidance_scale": 7.5
    }
  }
}
```

#### 配置使用最佳实践

**1. 配置访问模式**
```python
# 推荐：使用嵌套键访问
temperature = get_config('generators', 'framework_generation.temperature', default=0.7)

# 推荐：获取整个配置节
image_config = get_section('generators', 'image_generation')
steps = image_config.get('default_steps', 25)

# 动态配置修改
set_config('api', 'openai.model', 'deepseek-reasoner')
```

**2. 配置验证和错误处理**
```python
from src.config import get_config_manager

# 验证配置文件
manager = get_config_manager()
if not manager.validate_config('api'):
    print("API配置文件无效")

# 列出所有配置
configs = manager.list_configs()
print(f"可用配置: {configs}")
```

**3. 环境特定配置**
- **开发环境**: 使用较小的batch_size和timeout值
- **生产环境**: 启用缓存和速率限制
- **测试环境**: 禁用外部API调用，使用mock数据

### 故障排除指南
1. **配置文件问题**: 使用`validate_config()`检查JSON格式
2. **API密钥问题**: 检查config_api.json中的密钥配置
3. **模型路径问题**: 验证config_preprocessor.json中的模型路径
4. **内存问题**: 调整config_pipeline.json中的memory_limit_gb
5. **权限问题**: 检查paths.py定义的目录权限

## 9. 使用示例 (Usage Examples)

### CLI完整游戏生成流程
```bash
# 启动CLI界面
python launcher.py

# 选择菜单选项
# 1. 🎮 生成完整游戏
# 输入主题: 雷州石狗文化遗产
# 选择素材文件: example_materials/雷州石狗文化.txt
```

### 编程接口调用示例
```python
# 1. 准备素材
material_paths = ["example_materials/雷州石狗文化.txt"]

# 2. 启动生成
from src.ai_workflow.main_pipeline_controller import MainPipelineController
controller = MainPipelineController()

# 完整流程生成
result = controller.generate_complete_game(
    material_paths=material_paths,
    theme="雷州石狗文化遗产",
    keywords=["石狗传说", "雷州历史"]
)

# 3. 获取结果
print(f"游戏生成完成: {result['final_output']['game_directory']}")
print(f"生成统计: {result['completion_report']['content_statistics']}")

# 4. 单步调用示例
# 只进行素材预处理
preprocess_result = controller.material_preprocessor.process_materials(material_paths)

# 只构建RAG索引
rag_result = controller.rag_retrieval.build_index()

# 只生成游戏框架
framework_result = controller.framework_generator.generate_story_framework(
    theme="雷州石狗文化遗产",
    reference_materials=["石狗传说"]
)
```

### API调用示例
```python
import requests

# 异步调用游戏生成
response = requests.post("http://127.0.0.1:8080/api/v1/call-async", json={
    "service": "generate_complete_game",
    "params": {
        "theme": "雷州石狗文化遗产",
        "material_paths": ["example_materials/雷州石狗文化.txt"]
    }
})

task_id = response.json()["task_id"]

# 查询任务状态
status_response = requests.get(f"http://127.0.0.1:8080/api/v1/task/{task_id}")
print(status_response.json())
```

### 测试功能示例
```bash
# 测试素材预处理
curl -X POST "http://127.0.0.1:8080/api/v1/call" \
  -H "Content-Type: application/json" \
  -d '{"service": "test_material_preprocessing", "params": {}}'

# 测试RAG检索
curl -X POST "http://127.0.0.1:8080/api/v1/call" \
  -H "Content-Type: application/json" \
  -d '{"service": "test_rag_retrieval", "params": {"query": "石狗传说"}}'
```

## 10. 扩展开发 (Extension Development)

### 新生成器模块开发

#### 开发步骤
1. **创建生成器文件**: 在`src/ai_workflow/generators/`下创建新生成器
2. **实现标准接口**: 参考现有生成器的实现模式
3. **添加配置支持**: 在`config_generators.json`中添加配置项
4. **集成到主控制器**: 在`MainPipelineController`中添加@property方法
5. **添加提示词**: 在`prompts/`目录下创建对应的提示词文件

#### 自定义生成器示例
```python
# src/ai_workflow/generators/custom_generator.py
import os
from src.config import get_section
from src.ai_workflow.utils.gpt_client import gpt
from src.ai_workflow.rag.rag_retrieval import RAGRetrieval

class CustomGenerator:
    def __init__(self):
        # 获取配置
        self.config = get_section('generators', 'custom_generation')

        # 初始化RAG(如果需要)
        self.rag = RAGRetrieval()

        # 设置输出目录
        self.output_dir = self.config.get('output_directory', 'output/custom')
        os.makedirs(self.output_dir, exist_ok=True)

    def generate(self, input_data, theme=None):
        # RAG检索增强(可选)
        if theme:
            context = self.rag.search_text("chunks", theme, top_k=3)

        # 调用LLM生成
        result = gpt(
            system_prompt="你是一个内容生成器...",
            user_prompt=f"基于以下信息生成内容: {input_data}",
            temperature=self.config.get('temperature', 0.7)
        )

        return {"generated_content": result, "metadata": {"theme": theme}}
```

#### 集成到主控制器
```python
# 在MainPipelineController中添加
@property
def custom_generator(self):
    if self._custom_generator is None:
        self._custom_generator = CustomGenerator()
    return self._custom_generator

def cleanup_custom_generator(self):
    if self._custom_generator is not None:
        del self._custom_generator
        self._custom_generator = None
```

### 新服务接口开发
```python
# src/backend/game_services.py
@register_service(
    "custom_service",
    "自定义服务描述",
    {
        "param1": {"type": "str", "required": True},
        "param2": {"type": "int", "required": False}
    }
)
def custom_service(param1: str, param2: int = 10):
    # 实现服务逻辑
    return {"status": "success", "data": result}
```

### 配置文件扩展
在相应的JSON配置文件中添加新的配置项：
```json
// config_generators.json
{
  "custom_generation": {
    "model": "deepseek-reasoner",
    "temperature": 0.7,
    "max_tokens": 1000
  }
}
```

### 开发规范与最佳实践

#### 代码规范
- **命名规范**: 类名使用PascalCase，方法名使用snake_case
- **文档字符串**: 所有公共方法应包含详细的docstring
- **类型注解**: 使用typing模块进行类型标注
- **错误处理**: 使用try-except进行异常捕获，记录详细日志

#### 配置管理最佳实践
```python
# 推荐的配置获取方式
from src.config import get_section

# 获取生成器配置
config = get_section('generators', 'image_generation')
steps = config.get('default_steps', 25)  # 提供默认值
```

#### RAG集成最佳实践
```python
# 在生成器中集成RAG检索
class CustomGenerator:
    def __init__(self):
        self.rag = RAGRetrieval()

    def generate_with_context(self, theme, query):
        # 检索相关上下文
        context = self.rag.search_text("chunks", query, top_k=3)

        # 构建增强提示词
        context_text = "\n".join([item["content"] for item in context])
        enhanced_prompt = f"基于以下背景:\n{context_text}\n\n生成关于{theme}的内容"

        return enhanced_prompt
```

#### 性能建议
- **按需加载**: 使用@property装饰器实现模块的懒加载
- **内存管理**: 及时清理不再使用的模型和数据
- **批处理**: 对于大量数据处理，使用批处理方式
- **缓存机制**: 对频繁访问的数据实现缓存

#### 测试与调试
```python
# 单元测试示例
def test_material_preprocessor():
    preprocessor = MaterialPreprocessor()
    result = preprocessor.process_materials(["test_file.txt"])
    assert result["text_files"] is not None
    assert len(result["processed_files"]) > 0
```

---

*本文档基于遗游记 v2.0.0实际代码结构编写，涵盖了项目的核心技术架构和实现细节。如有疑问请参考源码或联系开发团队。*
