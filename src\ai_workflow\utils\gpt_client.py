"""
OpenAI GPT客户端
使用官方OpenAI库进行API调用
"""

from typing import List, Dict, Optional, Any
import logging

try:
    from openai import OpenAI
except ImportError:
    OpenAI = None

from src.config import get_config

# 设置日志
logger = logging.getLogger(__name__)


class GPTClient:
    """OpenAI GPT客户端类"""

    def __init__(self, api_key: Optional[str] = None, base_url: Optional[str] = None,
                 model: Optional[str] = None, temperature: Optional[float] = None):
        """
        初始化GPT客户端

        Args:
            api_key: OpenAI API密钥，None时从配置获取
            base_url: API基础URL，None时从配置获取
            model: 模型名称，None时从配置获取
            temperature: 温度参数，None时从配置获取
        """
        if OpenAI is None:
            raise ImportError("请安装openai库: pip install openai")

        # 获取配置
        config = get_config("api", "openai")

        self.api_key = api_key or config.get("api_key")
        self.base_url = base_url or config.get("base_url")
        self.model = model or config.get("model", "gpt-3.5-turbo")
        self.temperature = temperature or config.get("temperature", 0.7)

        if not self.api_key:
            raise ValueError("API密钥未设置，请在配置文件中设置或传入参数")

        # 初始化OpenAI客户端
        self.client = OpenAI(
            api_key=self.api_key,
            base_url=self.base_url
        )

        logger.info(f"GPT客户端初始化完成，模型: {self.model}")

    def chat(self, system: str, prompt: str, history: Optional[List[Dict[str, str]]] = None,
             model: Optional[str] = None, temperature: Optional[float] = None,
             max_tokens: Optional[int] = None) -> str:
        """
        进行对话

        Args:
            system: 系统提示词
            prompt: 用户提示词
            history: 对话历史
            model: 模型名称，None时使用默认模型
            temperature: 温度参数，None时使用默认值
            max_tokens: 最大token数

        Returns:
            GPT回复内容
        """
        try:
            # 构建消息列表
            messages = []

            # 添加历史对话
            if history:
                messages.extend(history)

            # 添加系统消息和用户消息
            messages.extend([
                {"role": "system", "content": system},
                {"role": "user", "content": prompt}
            ])

            # 调用OpenAI API
            response = self.client.chat.completions.create(
                model=model or self.model,
                messages=messages,
                temperature=temperature or self.temperature,
                max_tokens=max_tokens
            )

            return response.choices[0].message.content

        except Exception as e:
            logger.error(f"GPT API调用失败: {e}")
            return f"API调用失败: {str(e)}"

    def simple_chat(self, prompt: str, system: Optional[str] = None) -> str:
        """
        简单对话（无历史记录）

        Args:
            prompt: 用户提示词
            system: 系统提示词，None时使用默认值

        Returns:
            GPT回复内容
        """
        default_system = "你是一个有用的AI助手。"
        return self.chat(system or default_system, prompt)


# 全局客户端实例
_global_client = None


def get_gpt_client() -> GPTClient:
    """获取全局GPT客户端实例"""
    global _global_client
    if _global_client is None:
        _global_client = GPTClient()
    return _global_client


def gpt(system: str, prompt: str, mode: str = "common", history: Optional[List[Dict[str, str]]] = None) -> str:
    """
    兼容性函数，保持原有接口

    Args:
        system: 系统提示词
        prompt: 用户提示词
        mode: 模式（保留参数，暂未使用）
        history: 对话历史

    Returns:
        GPT回复内容
    """
    client = get_gpt_client()
    return client.chat(system, prompt, history)
