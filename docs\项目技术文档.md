# 🎮 遗游记 项目技术文档

## 📋 目录
- [开发框架与技术栈](#开发框架与技术栈)
- [项目架构设计](#项目架构设计)
- [核心模块详解](#核心模块详解)
- [数据存储结构](#数据存储结构)
- [API接口规范](#api接口规范)
- [配置管理系统](#配置管理系统)
- [部署与运行](#部署与运行)

---

## 🛠️ 开发框架与技术栈

### 核心技术栈
```yaml
语言环境:
  - Python: 3.10+
  - 操作系统: Windows/Linux/MacOS

AI/ML框架:
  - 大语言模型: OpenAI GPT/Deepseek-R1
  - 图像生成: Stable Diffusion 1.5/SDXL
  - 语音合成: GPT-SoVITS/Edge-TTS
  - 音乐生成: MusicGen
  - 向量检索: FAISS/Numpy

核心依赖:
  - FastAPI: Web API框架
  - numpy: 数值计算
  - torch: 深度学习
  - transformers: 预训练模型
  - Pillow: 图像处理
  - scikit-learn: 机器学习工具
```

### 设计理念
- **配置驱动**: 所有参数通过配置文件管理，支持热更新
- **RAG优先**: 检索增强生成，动态获取相关上下文
- **JSON标准化**: 生成器统一输出JSON格式，便于解析
- **模块化设计**: 松耦合架构，支持独立开发和测试
- **资源管理**: 按需加载模型，自动释放GPU内存

---

## 🏗️ 项目架构设计

### 分层架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    API接口层 (FastAPI)                      │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │   server.py │  │  router.py  │  │ middleware  │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      服务层                                 │
│  ┌─────────────────────────────────────────────────────┐   │
│  │            service_registry.py                      │   │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐ │   │
│  │  │   服务注册   │  │   服务发现   │  │   调用管理   │ │   │
│  │  └─────────────┘  └─────────────┘  └─────────────┘ │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      业务层                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ 预处理模块   │  │ RAG检索系统  │  │ 生成器模块   │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ 集成模块     │  │ 流程控制     │  │ 工具模块     │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
```

### 目录结构
```
遗游记/
├── src/
│   ├── config/                    # 配置管理
│   │   ├── paths.py              # 路径常量
│   │   ├── config_generators.json # 生成器配置
│   │   └── __init__.py           # 配置读取
│   │
│   └── ai_workflow/
│       ├── preprocess/           # 预处理模块
│       │   ├── text_preprocessor/ # 文本预处理
│       │   └── image_preprocessor/ # 图像预处理
│       │
│       ├── rag/                  # RAG检索系统
│       │   ├── text/            # 文本RAG
│       │   └── image/           # 图像RAG
│       │
│       ├── generators/          # 生成器模块
│       │   ├── prompts/         # 提示词库
│       │   ├── game_framework_generator.py
│       │   ├── node_narrative_generator.py
│       │   ├── image_generator.py
│       │   ├── vocal_generator.py
│       │   └── music_generator.py
│       │
│       ├── integrators/         # 集成模块
│       │   └── renpy_integrator.py
│       │
│       ├── utils/               # 工具模块
│       │   └── gpt_client.py
│       │
│       └── main_pipeline_controller.py # 主流程控制
│
├── data/                        # 数据目录
│   ├── preprocess/              # 预处理数据
│   ├── embedding/               # 向量数据
│   ├── framework/               # 框架数据
│   └── output/                  # 输出结果
│
└── docs/                        # 文档目录
```

---

## 🔧 核心模块详解

### 1. 预处理模块 (preprocess/)

#### 文本预处理器 (text_preprocessor/)
```python
# 核心功能
- 文档解析: PDF/TXT/DOCX → 结构化文本
- 智能分段: 基于语义的段落切分
- 摘要生成: 段落级抽象摘要
- 细分块化: 适合检索的文本块

# 主要类
class TextPreprocessor:
    def process_documents(self, file_paths: List[str]) -> Dict
    def segment_text(self, text: str) -> List[Dict]
    def generate_summaries(self, segments: List[Dict]) -> List[Dict]
    def create_chunks(self, segments: List[Dict]) -> List[Dict]
```

#### 图像预处理器 (image_preprocessor/)
```python
# 核心功能
- 图像标准化: 尺寸/格式统一
- 语义提取: CLIP-Interrogator描述生成
- 质量过滤: 低质量图像筛除

# 主要类
class ImagePreprocessor:
    def process_images(self, image_dir: str) -> Dict
    def extract_descriptions(self, images: List[str]) -> List[Dict]
    def filter_quality(self, images: List[str]) -> List[str]
```

### 2. RAG检索系统 (rag/)

#### 文本RAG (text/)
```python
# 核心组件
- EmbeddingTool: 文本向量化与存储
- Retriever: 索引构建与检索
- TextRAG: 统一检索接口

# 数据格式
全局向量文件:
- all_segments.npz / all_segments_metadata.json
- all_chunks.npz / all_chunks_metadata.json  
- all_segments_summary.npz / all_segments_summary_metadata.json

# 检索接口
def search(dtype: str, query: str, top_k: int) -> List[Dict]:
    return [
        {
            "content": "文本内容",
            "source": "来源文件",
            "similarity": 0.85
        }
    ]
```

#### 图像RAG (image/)
```python
# 核心组件
- CLIPImageIndexer: 图像向量化与索引
- TextImageRetriever: 文本到图像检索
- ImageRAG: 检索增强图像生成

# 索引结构
index_data/:
- faiss_index.index      # FAISS向量索引
- image_paths.json       # 图像路径映射
- clip_embeddings.npy    # CLIP向量数据

# 检索接口
def search_similar_images(text: str, top_k: int) -> List[Tuple[str, float]]
```

### 3. 生成器模块 (generators/)

#### 框架生成器 (game_framework_generator.py)
```python
# JSON输出格式
{
  "mainline_nodes": [
    {
      "node_id": "A1",
      "description": "节点描述", 
      "key_events": ["事件1", "事件2"],
      "characters": ["角色1", "角色2"]
    }
  ],
  "sideline_nodes": [...],
  "node_relationships": [...]
}

# 角色设定格式
[
  {
    "name": "角色姓名",
    "age": 25,
    "gender": "性别",
    "appearance": "外貌描述",
    "personality": "性格特点", 
    "background": "背景故事",
    "motivation": "动机目标",
    "role": "剧情作用",
    "relationships": {},
    "is_protagonist": false
  }
]
```

#### 叙事生成器 (node_narrative_generator.py)
```python
# JSON输出格式
{
  "settings": ["场景1", "场景2"],
  "music_instructions": ["音乐1", "音乐2"],
  "sound_effects": ["音效1", "音效2"],
  "cg_scenes": ["CG描述1", "CG描述2"],
  "dialogues": [
    {
      "character": "角色名",
      "emotion": "情感/动作", 
      "content": "对话内容"
    }
  ],
  "narrations": ["旁白1", "旁白2"],
  "characters": [
    {"name": "角色名", "description": "角色描述"}
  ],
  "raw_text": "原始剧本文本"
}
```

#### 图像生成器 (image_generator.py)
```python
# 配置驱动生成
class ImageGenerator:
    def generate_background(self, prompt: str, **kwargs) -> PIL.Image
    def generate_character(self, prompt: str, **kwargs) -> PIL.Image  
    def generate_cg(self, prompt: str, **kwargs) -> PIL.Image

# RAG增强
- 检索相关参考图像
- IP-Adapter风格融合
- 提示词优化增强
```

#### 语音生成器 (vocal_generator.py)
```python
# 角色音色映射
class VocalGenerator:
    def generate_voice(self, text: str, character: str) -> bytes
    def batch_generate(self, dialogues: List[Dict]) -> Dict[str, bytes]

# 配置格式
{
  "character_voices": {
    "主角": {"voice_id": "default", "speed": 1.0},
    "角色A": {"voice_id": "female_young", "speed": 0.9}
  }
}
```

#### 音乐生成器 (music_generator.py)
```python
# 场景音乐生成
class MusicGenerator:
    def generate_bgm(self, description: str, duration: int) -> bytes
    def generate_sound_effect(self, description: str) -> bytes

# 风格配置
{
  "music_styles": {
    "古典": "classical, traditional chinese",
    "现代": "modern, electronic",
    "悬疑": "mysterious, dark ambient"
  }
}
```

---

## 💾 数据存储结构

### 向量数据库结构
```
data/embedding/
├── text/                        # 文本向量数据
│   ├── all_segments.npz         # 段落向量
│   ├── all_segments_metadata.json
│   ├── all_chunks.npz           # 细分块向量
│   ├── all_chunks_metadata.json
│   ├── all_segments_summary.npz # 摘要向量
│   └── all_segments_summary_metadata.json
│
└── image/                       # 图像向量数据
    ├── faiss_index.index        # FAISS索引文件
    ├── image_paths.json         # 图像路径映射
    └── clip_embeddings.npy      # CLIP向量数据
```

### 元数据格式
```json
// 文本元数据示例
{
  "source_name": "文件名",
  "segment_index": 0,
  "chunk_index": 0, 
  "text": "文本内容",
  "char_count": 150,
  "global_index": 0
}

// 图像元数据示例
{
  "image_path": "path/to/image.jpg",
  "description": "图像描述",
  "tags": ["标签1", "标签2"],
  "embedding_index": 0
}
```

### 产物存储结构
```
data/output/
├── framework/                   # 框架数据
│   └── game_framework.json
├── narratives/                  # 叙事数据
│   ├── node_A1_narrative.json
│   └── node_A2_narrative.json
├── images/                      # 生成图像
│   ├── backgrounds/
│   ├── characters/
│   └── cg/
├── audio/                       # 生成音频
│   ├── voices/
│   └── music/
└── renpy_project/              # Ren'Py项目
    ├── game/
    ├── images/
    └── audio/
```

---

## 🌐 API接口规范

### 服务注册机制
```python
# 服务注册装饰器
@register_service("service_name", "服务描述", {
    "param1": {"type": "str", "description": "参数描述"},
    "param2": {"type": "int", "default": 10}
})
def service_function(param1: str, param2: int = 10) -> Dict:
    return {"status": "success", "data": result}
```

### 核心API端点
```yaml
# 同步服务调用
POST /api/v1/call:
  body:
    service_name: "generate_complete_game"
    parameters:
      theme: "石狗文化"
      material_paths: ["path1", "path2"]
  response:
    status: "success|error"
    message: "执行结果描述"
    data: {...}

# 异步服务调用  
POST /api/v1/call-async:
  body: (同上)
  response:
    task_id: "uuid-string"
    status: "pending"

# 任务状态查询
GET /api/v1/task/{task_id}:
  response:
    task_id: "uuid-string"
    status: "pending|running|completed|failed"
    result: {...}  # 仅在completed时存在

# 服务发现
GET /api/v1/services:
  response:
    services: [
      {
        "name": "service_name",
        "description": "服务描述",
        "parameters": {...}
      }
    ]
```

### 业务服务接口
```python
# 游戏生成服务
def generate_complete_game(theme: str, material_paths: List[str]) -> Dict

# 素材预处理服务  
def test_material_preprocessing(input_dir: str) -> Dict

# RAG检索服务
def test_rag_retrieval(query: str, top_k: int = 5) -> Dict

# 系统测试服务
def run_system_test() -> Dict
```

---

## ⚙️ 配置管理系统

### 主配置文件 (config_generators.json)
```json
{
  "framework_generation": {
    "max_nodes": 10,
    "default_character": {
      "name": "主角",
      "description": "平凡的年轻人"
    }
  },
  "image_generation": {
    "default_size": [512, 512],
    "steps": 20,
    "guidance_scale": 7.5,
    "models": {
      "background": "sd15-anything-v5",
      "character": "sd15-anime",
      "cg": "sdxl-base"
    }
  },
  "audio_generation": {
    "voice_models": {
      "default": "edge-tts",
      "high_quality": "gpt-sovits"
    },
    "music_model": "musicgen-medium"
  }
}
```

### 路径配置 (paths.py)
```python
# 数据路径
PREPROCESS_TEXT_DIR = Path("data/preprocess/text")
PREPROCESS_IMAGE_DIR = Path("data/preprocess/image")
EMBEDDING_DB_DIR = Path("data/embedding")
RAG_TEXT_DIR = Path("data/rag/text")
RAG_IMAGE_DIR = Path("data/rag/image")

# 输出路径
FRAMEWORK_DIR = Path("data/output/framework")
OUTPUT_IMAGE_DIR = Path("data/output/images")
OUTPUT_AUDIO_DIR = Path("data/output/audio")
RENPY_PROJECT_DIR = Path("data/output/renpy_project")
```

### 提示词配置 (prompts/)
```python
# 框架生成提示词
FRAMEWORK_GENERATION_PROMPTS = {
    "story_skeleton": {
        "system": "...",
        "user": "..."
    }
}

# 叙事生成提示词  
NARRATIVE_GENERATION_PROMPTS = {
    "node_narrative": {
        "system": "...",
        "user": "..."
    }
}
```

---

## 🚀 部署与运行

### 环境准备
```bash
# 1. 安装Python依赖
pip install -r requirements.txt

# 2. 安装spaCy模型
python -m spacy download zh_core_web_sm

# 3. 配置环境变量
export OPENAI_API_KEY="your-api-key"
export OPENAI_BASE_URL="https://api.openai.com/v1"

# 4. 初始化数据目录
python -c "from src.config.paths import *; [p.mkdir(parents=True, exist_ok=True) for p in [PREPROCESS_TEXT_DIR, EMBEDDING_DB_DIR, OUTPUT_IMAGE_DIR]]"
```

### 启动服务
```bash
# 开发模式
python -m uvicorn src.ai_workflow.server:app --reload --host 0.0.0.0 --port 8000

# 生产模式
python -m uvicorn src.ai_workflow.server:app --host 0.0.0.0 --port 8000 --workers 4
```

### 使用示例
```python
import requests

# 同步调用
response = requests.post("http://localhost:8000/api/v1/call", json={
    "service_name": "generate_complete_game",
    "parameters": {
        "theme": "石狗文化",
        "material_paths": ["data/materials/stone_dog.txt"]
    }
})

# 异步调用
task_response = requests.post("http://localhost:8000/api/v1/call-async", json={
    "service_name": "generate_complete_game", 
    "parameters": {...}
})
task_id = task_response.json()["task_id"]

# 查询任务状态
status_response = requests.get(f"http://localhost:8000/api/v1/task/{task_id}")
```

### 性能优化
```yaml
硬件要求:
  - CPU: 8核以上推荐
  - 内存: 16GB以上
  - GPU: 8GB显存以上(可选)
  - 存储: 50GB以上可用空间

优化建议:
  - 使用GPU加速图像/音频生成
  - 配置合适的worker数量
  - 启用模型缓存机制
  - 定期清理临时文件
```

---

## 📊 监控与日志

### 日志配置
```python
# 日志级别配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/ai_gal.log'),
        logging.StreamHandler()
    ]
)
```

### 性能监控
```python
# 内置监控指标
- API响应时间
- 服务调用成功率  
- GPU内存使用率
- 任务队列长度
- 错误率统计
```

---

## 🔍 详细实现说明

### 1. RAG检索系统实现细节

#### 文本向量化流程
```python
# 1. 文本预处理
segments = text_preprocessor.segment_text(raw_text)
chunks = text_preprocessor.create_chunks(segments)

# 2. 向量化存储
embedding_tool = EmbeddingTool()
embedding_tool.embed_segments(segments, source_name)  # → all_segments.npz
embedding_tool.embed_chunks(chunks, source_name)      # → all_chunks.npz

# 3. 索引构建
retriever = Retriever(data_type='segments')
retriever.load_index()  # 自动从all_segments.npz构建FAISS索引

# 4. 检索查询
results = retriever.search_text("查询文本", embedding_tool, top_k=5)
```

#### 图像检索流程
```python
# 1. 图像索引构建
indexer = CLIPImageIndexer()
indexer.build_index_from_directory("data/preprocess/image")
indexer.save_index()

# 2. 文本到图像检索
retriever = TextImageRetriever()
retriever.load_index()
similar_images = retriever.search_similar_images("石狗雕像", top_k=3)

# 3. 检索增强生成
image_rag = ImageRAG()
generated_image = image_rag.generate(
    prompt="古代石狗雕像",
    use_retrieval=True,  # 使用检索到的参考图像
    ip_adapter_scale=0.5
)
```

### 2. 生成器详细配置

#### 图像生成器配置
```json
{
  "image_generation": {
    "models": {
      "background": {
        "model_path": "models/sd15-anything-v5",
        "default_size": [768, 512],
        "steps": 25,
        "guidance_scale": 7.5,
        "negative_prompt": "lowres, bad anatomy, bad hands"
      },
      "character": {
        "model_path": "models/sd15-anime",
        "default_size": [512, 768],
        "steps": 30,
        "guidance_scale": 8.0,
        "negative_prompt": "nsfw, ugly, deformed"
      },
      "cg": {
        "model_path": "models/sdxl-base",
        "default_size": [1024, 1024],
        "steps": 35,
        "guidance_scale": 6.0
      }
    },
    "ip_adapter": {
      "enabled": true,
      "model_path": "models/ip-adapter-plus",
      "scale": 0.5
    }
  }
}
```

#### 语音生成器配置
```json
{
  "audio_generation": {
    "voice_models": {
      "edge_tts": {
        "voices": {
          "male_young": "zh-CN-YunxiNeural",
          "female_young": "zh-CN-XiaoxiaoNeural",
          "male_old": "zh-CN-YunyangNeural",
          "female_old": "zh-CN-XiaochenNeural"
        }
      },
      "gpt_sovits": {
        "model_path": "models/gpt-sovits",
        "reference_audio_dir": "data/reference_voices"
      }
    },
    "character_mapping": {
      "主角": {"model": "edge_tts", "voice": "male_young"},
      "祖母": {"model": "edge_tts", "voice": "female_old"},
      "传承人": {"model": "edge_tts", "voice": "male_old"}
    }
  }
}
```

### 3. 流程控制器实现

#### 主流程控制器
```python
class MainPipelineController:
    def __init__(self):
        self._text_preprocessor = None
        self._image_preprocessor = None
        self._text_rag = None
        self._image_rag = None
        self._framework_generator = None
        self._narrative_generator = None
        self._image_generator = None
        self._vocal_generator = None
        self._music_generator = None
        self._renpy_integrator = None

    @property
    def text_preprocessor(self):
        if self._text_preprocessor is None:
            self._text_preprocessor = TextPreprocessor()
        return self._text_preprocessor

    def cleanup_module(self, module_name: str):
        """清理指定模块，释放GPU内存"""
        if hasattr(self, f'_{module_name}'):
            setattr(self, f'_{module_name}', None)
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

    def generate_complete_game(self, material_paths: List[str], theme: str) -> Dict:
        """端到端游戏生成流程"""
        results = {}

        try:
            # 步骤1: 素材预处理
            results["preprocessing"] = self._step_1_material_preprocessing(material_paths)

            # 步骤2: RAG索引构建
            results["rag_indexing"] = self._step_2_rag_indexing()

            # 步骤3: 游戏框架生成
            results["framework"] = self._step_3_framework_generation(theme)

            # 步骤4: 节点叙事生成
            results["narratives"] = self._step_4_narrative_generation(results["framework"])

            # 步骤5: 多媒体生成
            results["multimedia"] = self._step_5_multimedia_generation(results["narratives"])

            # 步骤6: Ren'Py整合
            results["integration"] = self._step_6_renpy_integration(results)

            return {
                "status": "success",
                "message": "游戏生成完成",
                "data": results
            }

        except Exception as e:
            return {
                "status": "error",
                "message": f"游戏生成失败: {str(e)}",
                "data": results
            }
        finally:
            # 清理所有模块
            self.cleanup_all_modules()
```

### 4. 错误处理与容错机制

#### JSON解析容错
```python
def parse_with_fallback(self, response: str, parser_type: str) -> Dict:
    """JSON优先，文本解析后备的容错机制"""
    try:
        # 优先尝试JSON解析
        result = json.loads(response.strip())
        self.logger.info(f"{parser_type} JSON解析成功")
        return self._validate_and_fill_defaults(result, parser_type)

    except json.JSONDecodeError as e:
        self.logger.warning(f"{parser_type} JSON解析失败: {e}")
        self.logger.info(f"使用文本解析后备方案")

        # 使用正则表达式等文本解析方法
        if parser_type == "story_skeleton":
            return self._parse_story_text_fallback(response)
        elif parser_type == "character_settings":
            return self._parse_character_text_fallback(response)
        elif parser_type == "narrative":
            return self._parse_narrative_text_fallback(response)
        else:
            return self._generate_default_result(parser_type)
```

#### 模型加载容错
```python
def load_model_with_fallback(self, model_config: Dict) -> Any:
    """模型加载容错机制"""
    primary_path = model_config.get("primary_model")
    fallback_path = model_config.get("fallback_model")

    try:
        # 尝试加载主模型
        model = self._load_model(primary_path)
        self.logger.info(f"主模型加载成功: {primary_path}")
        return model

    except Exception as e:
        self.logger.warning(f"主模型加载失败: {e}")

        if fallback_path:
            try:
                # 尝试加载备用模型
                model = self._load_model(fallback_path)
                self.logger.info(f"备用模型加载成功: {fallback_path}")
                return model
            except Exception as e2:
                self.logger.error(f"备用模型加载失败: {e2}")

        # 使用默认配置
        return self._get_default_model()
```

### 5. 性能优化策略

#### GPU内存管理
```python
class GPUMemoryManager:
    def __init__(self):
        self.model_cache = {}
        self.max_cache_size = 3

    def get_model(self, model_name: str):
        """获取模型，自动管理缓存"""
        if model_name in self.model_cache:
            return self.model_cache[model_name]

        # 检查缓存大小
        if len(self.model_cache) >= self.max_cache_size:
            self._evict_oldest_model()

        # 加载新模型
        model = self._load_model(model_name)
        self.model_cache[model_name] = model
        return model

    def _evict_oldest_model(self):
        """移除最旧的模型"""
        oldest_key = next(iter(self.model_cache))
        del self.model_cache[oldest_key]
        torch.cuda.empty_cache()
```

#### 批处理优化
```python
def batch_process_with_progress(self, items: List, process_func, batch_size: int = 8):
    """批处理优化，支持进度跟踪"""
    results = []
    total_batches = (len(items) + batch_size - 1) // batch_size

    for i in range(0, len(items), batch_size):
        batch = items[i:i + batch_size]
        batch_results = []

        for item in batch:
            try:
                result = process_func(item)
                batch_results.append(result)
            except Exception as e:
                self.logger.error(f"处理项目失败: {e}")
                batch_results.append(None)

        results.extend(batch_results)

        # 进度报告
        current_batch = (i // batch_size) + 1
        progress = (current_batch / total_batches) * 100
        self.logger.info(f"批处理进度: {current_batch}/{total_batches} ({progress:.1f}%)")

        # 内存清理
        if current_batch % 3 == 0:
            torch.cuda.empty_cache()

    return results
```

### 6. 测试与质量保证

#### 单元测试示例
```python
import unittest
from src.ai_workflow.rag.text.text_rag import TextRAG

class TestTextRAG(unittest.TestCase):
    def setUp(self):
        self.text_rag = TextRAG()

    def test_search_segments(self):
        """测试段落检索功能"""
        results = self.text_rag.search("segments", "石狗文化", top_k=3)

        self.assertIsInstance(results, list)
        self.assertLessEqual(len(results), 3)

        for result in results:
            self.assertIn("content", result)
            self.assertIn("source", result)
            self.assertIn("similarity", result)
            self.assertIsInstance(result["similarity"], float)

    def test_search_chunks(self):
        """测试细分块检索功能"""
        results = self.text_rag.search("chunks", "传承人故事", top_k=5)

        self.assertIsInstance(results, list)
        self.assertLessEqual(len(results), 5)

class TestGameFrameworkGenerator(unittest.TestCase):
    def setUp(self):
        self.generator = GameFrameworkGenerator()

    def test_json_parsing(self):
        """测试JSON解析功能"""
        json_response = '''
        {
            "mainline_nodes": [
                {
                    "node_id": "A1",
                    "description": "测试节点",
                    "key_events": ["事件1"],
                    "characters": ["角色1"]
                }
            ],
            "sideline_nodes": [],
            "node_relationships": []
        }
        '''

        result = self.generator.parse_story_skeleton(json_response)

        self.assertIn("mainline_nodes", result)
        self.assertEqual(len(result["mainline_nodes"]), 1)
        self.assertEqual(result["mainline_nodes"][0]["node_id"], "A1")
```

#### 集成测试
```python
def test_end_to_end_generation():
    """端到端生成测试"""
    controller = MainPipelineController()

    # 准备测试数据
    test_materials = ["tests/data/test_material.txt"]
    test_theme = "测试主题"

    # 执行完整流程
    result = controller.generate_complete_game(test_materials, test_theme)

    # 验证结果
    assert result["status"] == "success"
    assert "framework" in result["data"]
    assert "narratives" in result["data"]
    assert "multimedia" in result["data"]
```

### 7. 部署与运维

#### Docker部署
```dockerfile
FROM python:3.10-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    git \
    wget \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt .
RUN pip install -r requirements.txt

# 下载spaCy模型
RUN python -m spacy download zh_core_web_sm

# 复制源代码
COPY src/ ./src/
COPY docs/ ./docs/

# 创建数据目录
RUN mkdir -p data/preprocess data/embedding data/output

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "-m", "uvicorn", "src.ai_workflow.server:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### 监控配置
```python
# 健康检查端点
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
        "gpu_available": torch.cuda.is_available(),
        "gpu_memory": torch.cuda.get_device_properties(0).total_memory if torch.cuda.is_available() else None
    }

# 指标收集
@app.middleware("http")
async def metrics_middleware(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time

    # 记录指标
    logger.info(f"API调用: {request.url.path} - {response.status_code} - {process_time:.3f}s")

    return response
```

---

这份完整的技术文档为遗游记项目提供了详尽的实现指南，涵盖了从架构设计到部署运维的各个方面，是开发团队的重要参考资料。
