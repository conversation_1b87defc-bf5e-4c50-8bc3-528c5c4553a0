# langsegment_stub.py
import re

class LangSegment:
    _filters = None

    @classmethod
    def setfilters(cls, filters):
        cls._filters = set(filters)

    @classmethod
    def getTexts(cls, text):
        """
        模拟原LangSegment.getTexts输出
        返回: [{"lang": lang_code, "text": segment_text}, ...]
        """
        segments = []
        # 按中英文/日文/韩文/数字/符号切割
        tokens = re.findall(
            r'[\u4e00-\u9fff]+|'      # 中文
            r'[ぁ-ゟ゠-ヿ]+|'          # 日文
            r'[ㄱ-ㅎㅏ-ㅣ가-힣]+|'     # 韩文
            r'[a-zA-Z]+|'              # 英文
            r'\d+|'                    # 数字
            r'\S',                     # 其他符号
            text
        )
        for tok in tokens:
            lang = cls._detect_lang(tok)
            if cls._filters is None or lang in cls._filters:
                segments.append({"lang": lang, "text": tok})
        return segments

    @staticmethod
    def _detect_lang(token):
        if re.fullmatch(r'[a-zA-Z]+', token):
            return "en"
        elif re.fullmatch(r'[\u4e00-\u9fff]+', token):
            return "zh"
        elif re.fullmatch(r'[ぁ-ゟ゠-ヿ]+', token):
            return "ja"
        elif re.fullmatch(r'[ㄱ-ㅎㅏ-ㅣ가-힣]+', token):
            return "ko"
        else:
            return "unk"
