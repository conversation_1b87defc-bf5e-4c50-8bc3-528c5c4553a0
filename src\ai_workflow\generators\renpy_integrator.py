"""
Ren'Py整合模块
将生成的文本、图像、音频整合成Ren'Py游戏脚本
"""

import os
import json
from typing import List, Dict

try:
    import renpy
    game_directory = renpy.config.gamedir
except:
    game_directory = os.getcwd()

class RenpyIntegrator:
    """Ren'Py脚本整合器"""
    
    def __init__(self):
        """初始化Ren'Py整合器"""
        self.output_dir = os.path.join(game_directory, "output", "renpy_game")
        os.makedirs(self.output_dir, exist_ok=True)
    
    def integrate_full_game(self, framework: Dict, narratives: List[Dict], 
                          image_results: List[Dict], audio_results: List[Dict]) -> str:
        """
        整合完整的Ren'Py游戏
        
        Args:
            framework: 游戏框架
            narratives: 节点叙事列表
            image_results: 图像生成结果
            audio_results: 音频生成结果
            
        Returns:
            生成的脚本文件路径
        """
        print("开始整合Ren'Py游戏脚本...")
        
        # 生成主脚本文件
        script_content = self._generate_main_script(framework, narratives, image_results, audio_results)
        
        # 保存脚本文件
        script_path = os.path.join(self.output_dir, "script.rpy")
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        print(f"Ren'Py脚本生成完成: {script_path}")
        return script_path
    
    def _generate_main_script(self, framework: Dict, narratives: List[Dict], 
                            image_results: List[Dict], audio_results: List[Dict]) -> str:
        """生成主脚本内容"""
        script_lines = []
        
        # 添加角色定义
        script_lines.append("# 角色定义")
        characters = framework.get("characters", [])
        for char in characters:
            char_name = char.get("name", "unknown")
            script_lines.append(f'define {char_name.lower()} = Character("{char_name}")')
        
        script_lines.append("")
        script_lines.append("# 游戏开始")
        script_lines.append("label start:")
        
        # 为每个节点生成脚本
        for i, narrative in enumerate(narratives):
            node_script = self._generate_node_script(narrative, image_results[i] if i < len(image_results) else {}, 
                                                   audio_results[i] if i < len(audio_results) else {})
            script_lines.extend(node_script)
        
        script_lines.append("    return")
        
        return "\n".join(script_lines)
    
    def _generate_node_script(self, narrative: Dict, images: Dict, audio: Dict) -> List[str]:
        """为单个节点生成脚本"""
        lines = []
        node_id = narrative.get("node_id", "unknown")
        content = narrative.get("narrative_content", {})
        
        lines.append(f"    # 节点 {node_id}")
        
        # 添加场景设置
        settings = content.get("settings", [])
        if settings:
            lines.append(f"    scene {settings[0]}")
        
        # 添加对话
        dialogues = content.get("dialogues", [])
        for dialogue in dialogues:
            character = dialogue.get("character", "narrator").lower()
            content_text = dialogue.get("content", "")
            lines.append(f'    {character} "{content_text}"')
        
        lines.append("")
        return lines
    
    def generate_options_file(self, framework: Dict):
        """生成选项配置文件"""
        options_content = f'''
# 游戏选项配置
define config.name = "{framework.get('theme', '文化遗产游戏')}"
define config.version = "1.0"
define config.save_directory = "game_saves"
'''
        
        options_path = os.path.join(self.output_dir, "options.rpy")
        with open(options_path, 'w', encoding='utf-8') as f:
            f.write(options_content)
    
    def generate_gui_file(self):
        """生成GUI配置文件"""
        gui_content = '''
# GUI配置
define gui.text_size = 22
define gui.name_text_size = 30
define gui.interface_text_size = 18
'''
        
        gui_path = os.path.join(self.output_dir, "gui.rpy")
        with open(gui_path, 'w', encoding='utf-8') as f:
            f.write(gui_content)
