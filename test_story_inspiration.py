#!/usr/bin/env python3
"""
测试故事灵感勘探功能
"""

import sys
import os
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.ai_workflow.preprocess.text_preprocessor.tools.summarizer import Summarizer
from src.ai_workflow.generators.game_framework_generator import GameFrameworkGenerator

def test_story_inspiration_extraction():
    """测试故事灵感勘探功能"""
    print("=== 测试故事灵感勘探功能 ===")
    
    # 创建测试文本
    test_text = """
    在古老的江南水乡，有一座传承了三百年的刺绣工坊。工坊的主人是一位年过七旬的老师傅，
    他的手艺精湛，能够在丝绸上绣出栩栩如生的花鸟鱼虫。然而，随着时代的变迁，年轻人
    对传统手工艺失去了兴趣，工坊面临着后继无人的困境。老师傅的孙女从城市回到家乡，
    她试图用现代的方式来传承这门古老的技艺，但却遭到了老师傅的反对。
    """
    
    try:
        # 初始化故事灵感勘探器
        summarizer = Summarizer(output_dir="test_output")
        
        # 勘探故事灵感
        inspiration = summarizer.extract_story_inspiration(test_text)
        
        print("故事灵感勘探结果：")
        print(json.dumps(inspiration, ensure_ascii=False, indent=2))
        
        return inspiration
        
    except Exception as e:
        print(f"故事灵感勘探测试失败: {e}")
        return None

def test_inspiration_aggregation():
    """测试灵感聚合功能"""
    print("\n=== 测试灵感聚合功能 ===")
    
    # 模拟多个灵感分析结果
    mock_analyses = [
        {
            "world_premises": ["古老的江南水乡", "传承三百年的工坊"],
            "dramatic_cores": ["传统与现代的冲突", "技艺传承的困境"],
            "character_archetypes": ["固执的老师傅", "现代化的年轻人"],
            "symbolic_resonances": ["刺绣代表传统文化", "丝绸象征精致与美"],
            "narrative_hooks": ["工坊面临关闭", "孙女回乡传承"]
        },
        {
            "world_premises": ["手工艺的黄金时代", "城市化进程"],
            "dramatic_cores": ["代际价值观冲突", "文化保护与发展"],
            "character_archetypes": ["文化守护者", "创新者"],
            "symbolic_resonances": ["针线连接过去与未来", "工坊是文化的载体"],
            "narrative_hooks": ["最后一件作品", "传统技艺的现代化"]
        }
    ]
    
    try:
        # 初始化游戏框架生成器
        generator = GameFrameworkGenerator()
        
        # 聚合灵感
        aggregated = generator.aggregate_inspirations(mock_analyses)
        
        print("灵感聚合结果：")
        print(json.dumps(aggregated, ensure_ascii=False, indent=2))
        
        return aggregated
        
    except Exception as e:
        print(f"灵感聚合测试失败: {e}")
        return None

def test_framework_generation_with_inspiration():
    """测试基于灵感的框架生成"""
    print("\n=== 测试基于灵感的框架生成 ===")
    
    # 使用前面的测试结果
    mock_analyses = [
        {
            "world_premises": ["古老的江南水乡", "传承三百年的工坊"],
            "dramatic_cores": ["传统与现代的冲突", "技艺传承的困境"],
            "character_archetypes": ["固执的老师傅", "现代化的年轻人"],
            "symbolic_resonances": ["刺绣代表传统文化", "丝绸象征精致与美"],
            "narrative_hooks": ["工坊面临关闭", "孙女回乡传承"]
        }
    ]
    
    try:
        # 初始化游戏框架生成器
        generator = GameFrameworkGenerator()
        
        # 生成游戏框架
        framework = generator.generate_story_framework(
            theme="传统文化传承",
            inspiration_analyses=mock_analyses
        )
        
        print("游戏框架生成结果：")
        print(json.dumps(framework, ensure_ascii=False, indent=2))
        
        return framework
        
    except Exception as e:
        print(f"基于灵感的框架生成测试失败: {e}")
        return None

if __name__ == "__main__":
    print("开始测试故事灵感勘探系统...")
    
    # 创建输出目录
    os.makedirs("test_output", exist_ok=True)
    
    # 运行测试
    inspiration = test_story_inspiration_extraction()
    aggregated = test_inspiration_aggregation()
    framework = test_framework_generation_with_inspiration()
    
    print("\n=== 测试完成 ===")
    if inspiration and aggregated and framework:
        print("所有测试通过！")
    else:
        print("部分测试失败，请检查错误信息。")
