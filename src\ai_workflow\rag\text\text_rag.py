"""
RAG系统模块
专注于向量化、检索和问答功能
"""

import logging
import json
from pathlib import Path
from typing import Dict, Any, List, Optional
import time
import sys

# 添加路径以导入其他模块
sys.path.append(str(Path(__file__).parent.parent.parent))

# 导入统一配置管理
from src.config import get_section
from src.config.paths import PREPROCESS_TEXT_DIR,RAG_TEXT_DIR

# 导入工具类
from .embedding_tool import EmbeddingTool
from .retriever import Retriever


class TextRAG:
    """
    RAG系统，专注于向量化、检索和问答功能
    """

    def __init__(self,
                 use_local_embedding: Optional[bool] = None,
                 embedding_model: Optional[str] = None,
                 api_key: Optional[str] = None,
                 base_url: Optional[str] = None):
        """
        初始化RAG系统

        Args:
            use_local_embedding: 是否使用本地嵌入模型（None时从配置获取）
            embedding_model: 嵌入模型名称（None时从配置获取）
            api_key: API密钥（None时从配置获取）
            base_url: API基础URL（None时从配置获取）
        """
        # 获取RAG配置
        rag_config = get_section('rag', 'embedding')
        api_config = get_section('api', 'openai')

        # 使用传入参数或配置默认值
        self.use_local_embedding = use_local_embedding if use_local_embedding is not None else rag_config.get('use_local', True)
        self.embedding_model = embedding_model if embedding_model is not None else rag_config.get('model', 'Qwen/Qwen3-Embedding-0.6B')
        self.api_key = api_key if api_key is not None else api_config.get('api_key', '')
        self.base_url = base_url if base_url is not None else api_config.get('base_url', '')

        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)

        # 初始化工具类
        self.embedding_tool = EmbeddingTool(
            use_local=self.use_local_embedding,
            model_name=self.embedding_model,
            api_key=self.api_key,
            base_url=self.base_url
        )
        self.retriever = Retriever(data_type='chunks')  # 默认处理segments类型

        self.logger.info("RAG系统初始化完成")

    def vectorize_segments(self, segments: List[Dict[str, Any]],
                           source_name: str,
                           use_summary: bool = False) -> List[Dict[str, Any]]:
        """
        为章节生成向量

        Args:
            segments: 章节列表
            source_name: 源文件名
            use_summary: 是否使用摘要生成向量

        Returns:
            包含向量的章节列表
        """
        self.logger.info(f"开始为章节生成向量: {source_name}")

        try:
            segments_with_embeddings = self.embedding_tool.embed_segments(
                segments, source_name, use_summary=use_summary
            )

            self.logger.info(f"章节向量化完成: {source_name}")
            return segments_with_embeddings

        except Exception as e:
            self.logger.error(f"章节向量化失败: {source_name}, 错误: {str(e)}")
            raise

    def vectorize_chunks(self, chunks: List[Dict[str, Any]],
                         source_name: str) -> List[Dict[str, Any]]:
        """
        为细分块生成向量

        Args:
            chunks: 细分块列表
            source_name: 源文件名

        Returns:
            包含向量的块列表
        """
        self.logger.info(f"开始为细分块生成向量: {source_name}")

        try:
            # 使用embedding_tool的embed_chunks方法
            chunks_with_embeddings = self.embedding_tool.embed_chunks(chunks, source_name)

            self.logger.info(f"细分块向量化完成: {source_name}, 共{len(chunks)}个块")
            return chunks_with_embeddings

        except Exception as e:
            self.logger.error(f"细分块向量化失败: {source_name}, 错误: {str(e)}")
            raise

    def build_index(self, chunks_with_embeddings: List[Dict[str, Any]],
                    source_name: str) -> None:
        """
        构建检索索引（已弃用，使用rebuild_retriever_index代替）

        Args:
            chunks_with_embeddings: 包含向量的块列表
            source_name: 源文件名
        """
        self.logger.warning("build_index方法已弃用，请使用rebuild_retriever_index")
        return self.rebuild_retriever_index()

    def rebuild_retriever_index(self) -> bool:
        """
        从全局向量文件重建检索器索引

        Returns:
            是否成功重建索引
        """
        try:
            # 检索器会自动从全局向量文件加载数据
            # 这里只需要确保检索器能够正确加载
            success = self.retriever.load_index()

            if success:
                stats = self.retriever.get_stats()
                self.logger.info(f"检索器索引重建完成，向量数量: {stats.get('vector_count', 0)}")
            else:
                self.logger.warning("检索器索引重建失败")

            return success

        except Exception as e:
            self.logger.error(f"检索器索引重建失败: {str(e)}")
            return False

    def search(self, dtype: str, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """
        搜索相关内容

        Args:
            dtype: 查询的库
            query: 查询文本
            top_k: 返回结果数量

        Returns:
            搜索结果列表
        """
        try:
            self.retriever=Retriever(data_type=dtype)
            results = self.retriever.search_text(query, self.embedding_tool, top_k)
            self.logger.info(f"搜索完成: '{query}', 返回{len(results)}个结果")
            return results
        except Exception as e:
            self.logger.error(f"搜索失败: {str(e)}")
            raise

    def process_text_data(self, text_result: Dict[str, Any],
                          vectorize_segments: bool = True,
                          vectorize_chunks: bool = True,
                          build_index: bool = True) -> Dict[str, Any]:
        """
        处理文本处理结果，进行向量化和索引构建

        Args:
            text_result: 文本处理结果
            vectorize_segments: 是否向量化章节
            vectorize_chunks: 是否向量化细分块
            build_index: 是否构建索引

        Returns:
            RAG处理结果
        """
        start_time = time.time()
        source_name = text_result['source_name']

        self.logger.info(f"开始RAG处理: {source_name}")

        try:
            rag_result = {
                'source_name': source_name,
                'vectorize_segments': vectorize_segments,
                'vectorize_chunks': vectorize_chunks,
                'build_index': build_index,
                'processing_time': 0
            }

            # 向量化章节
            if vectorize_segments and 'segments' in text_result:
                segments_with_embeddings = self.vectorize_segments(
                    text_result['segments'],
                    source_name,
                    use_summary='overall_summary' in text_result and text_result['overall_summary']
                )
                rag_result['segments_with_embeddings'] = segments_with_embeddings

            # 向量化细分块
            chunks_with_embeddings = None
            if vectorize_chunks and 'chunks' in text_result:
                chunks_with_embeddings = self.vectorize_chunks(
                    text_result['chunks'],
                    source_name
                )
                rag_result['chunks_with_embeddings'] = chunks_with_embeddings

            # 注意：索引构建在所有文件处理完后统一进行
            # 这里只标记是否需要构建索引
            rag_result['index_requested'] = build_index
            rag_result['index_built'] = False  # 将在process_preprocessed_files中统一构建

            rag_result['processing_time'] = time.time() - start_time

            # 保存RAG结果
            result_file = RAG_TEXT_DIR / f"{source_name}_rag_result.json"
            result_file.parent.mkdir(exist_ok=True)

            # 创建可序列化的结果（移除numpy数组）
            serializable_result = self._make_serializable(rag_result)
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(serializable_result, f, ensure_ascii=False, indent=2)

            self.logger.info(f"RAG处理完成: {source_name}, 耗时: {rag_result['processing_time']:.2f}秒")
            return rag_result

        except Exception as e:
            self.logger.error(f"RAG处理失败: {source_name}, 错误: {str(e)}")
            raise

    def save_index(self) -> str:
        """
        保存检索器索引（使用默认文件名）

        Returns:
            保存路径
        """
        return self.retriever.save_index()

    def get_stats(self) -> Dict[str, Any]:
        """
        获取RAG系统统计信息

        Returns:
            统计信息字典
        """
        return {
            'retriever_stats': self.retriever.get_stats(),
            'embedding_info': {
                'use_local': self.use_local_embedding,
                'model': self.embedding_model,
                'api_key_set': bool(self.api_key)
            },
            'directories': {
                'embeddings': len(list(Path("embeddings").glob("*.npz")))
            }
        }

    def _make_serializable(self, obj):
        """
        将对象转换为可序列化的格式

        Args:
            obj: 要转换的对象

        Returns:
            可序列化的对象
        """
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif hasattr(obj, 'tolist'):  # numpy数组
            return obj.tolist()
        elif isinstance(obj, Path):
            return str(obj)
        else:
            return obj

    def process_preprocessed_files(self, input_dir: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        处理预处理目录中的所有文本文件

        Args:
            input_dir: 输入目录路径，None时使用默认预处理文本目录

        Returns:
            处理结果列表
        """
        if input_dir is None:
            input_dir = str(PREPROCESS_TEXT_DIR)

        self.logger.info(f"Processing files from directory: {input_dir}")

        results = []
        input_path = Path(input_dir)

        if not input_path.exists():
            self.logger.warning(f"Input directory does not exist: {input_dir}")
            return results

        # 查找所有JSON文件
        json_files = list(input_path.glob("*_text_result.json"))

        if not json_files:
            self.logger.warning(f"No text result files found in {input_dir}")
            return results

        self.logger.info(f"Found {len(json_files)} text result files")

        for json_file in json_files:
            try:
                self.logger.info(f"Processing file: {json_file.name}")

                # 加载文本处理结果
                with open(json_file, 'r', encoding='utf-8') as f:
                    text_result = json.load(f)

                # 进行RAG处理
                rag_result = self.process_text_data(text_result)
                results.append(rag_result)

                self.logger.info(f"Successfully processed: {json_file.name}")

            except Exception as e:
                self.logger.error(f"Failed to process {json_file.name}: {e}")
                results.append({
                    "source_name": json_file.stem,
                    "error": str(e),
                    "status": "failed"
                })

        self.logger.info(f"Completed processing {len(results)} files")

        # 处理完所有文件后，重建检索器索引
        self.logger.info("重建检索器索引...")
        index_success = self.rebuild_retriever_index()

        # 添加索引状态到结果中
        for result in results:
            if isinstance(result, dict) and "error" not in result:
                result["final_index_built"] = index_success

        if index_success:
            self.logger.info("检索器索引重建成功")
        else:
            self.logger.warning("检索器索引重建失败")

        return results

    def get_global_vector_stats(self) -> Dict[str, Any]:
        """
        获取全局向量文件统计信息

        Returns:
            统计信息字典
        """
        try:
            # 获取embedding_tool的统计信息
            embedding_stats = self.embedding_tool.get_global_embeddings_stats()

            # 获取retriever的统计信息
            retriever_stats = self.retriever.get_stats()

            return {
                "embedding_files": embedding_stats,
                "retriever_index": retriever_stats,
                "files_location": {
                    "segments": str(self.embedding_tool.output_dir / "all_segments.npz"),
                    "chunks": str(self.embedding_tool.output_dir / "all_chunks.npz"),
                    "segments_summary": str(self.embedding_tool.output_dir / "all_segments_summary.npz")
                }
            }
        except Exception as e:
            self.logger.error(f"获取全局向量统计信息失败: {e}")
            return {"error": str(e)}
