{"workflow": {"total_steps": 9, "step_names": ["素材预处理", "RAG索引构建", "游戏框架生成", "节点叙事生成", "图像生成", "音频生成", "Ren'Py脚本整合", "UI设计生成", "最终输出"], "enable_step_validation": true, "continue_on_error": false, "save_intermediate_results": true}, "output": {"base_directory": "output", "timestamp_format": "%Y%m%d_%H%M%S", "create_subdirectories": true, "compress_output": false, "cleanup_temp_files": true}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file": "logs/pipeline.log", "max_file_size_mb": 100, "backup_count": 5, "console_output": true}, "performance": {"max_parallel_tasks": 4, "memory_limit_gb": 16, "timeout_per_step": 1800, "enable_progress_tracking": true, "auto_gc": true}, "validation": {"validate_inputs": true, "validate_outputs": true, "strict_mode": false, "error_tolerance": 0.2}, "recovery": {"enable_checkpoints": true, "checkpoint_interval": 1, "auto_resume": false, "max_retry_attempts": 3}}