# 新图像生成器 - RAG增强版

## 概述

新的 `ImageGenerator` 类整合了原有的提示词工程核心功能，并使用 RAG（检索增强生成）技术提升图像生成质量。

## 主要特性

- **RAG增强生成**: 基于CLIP检索相似图像，使用IP-Adapter增强生成
- **智能提示词工程**: 根据生成模式自动优化提示词
- **多种生成模式**: 支持背景图和角色图生成
- **批量处理**: 支持节点批量生成和角色立绘批量生成
- **简洁接口**: 保持原有API兼容性，使用更简单

## 核心改进

### 1. 提示词增强
```python
# 背景图模式
"masterpiece, wallpaper, 8k, detailed CG, {prompt}, (no_human)"

# 角色图模式  
"masterpiece, wallpaper, 8k, detailed CG, {prompt}, (upper_body), solo"
```

### 2. 智能尺寸选择
- 背景图: 960x540 (16:9比例)
- 角色图: 512x768 (2:3比例)

### 3. RAG增强
- 自动检索相似图像作为参考
- IP-Adapter融合技术提升生成质量
- 相似度阈值控制检索精度

## 使用方法

### 基础使用

```python
from src.ai_workflow.generators.image_generator import ImageGenerator

# 初始化
generator = ImageGenerator()

# 生成背景图
result = generator.generate_image(
    prompt="古代中国庭院，石桥流水",
    image_name="garden_bg",
    mode="background"
)

# 生成角色图
result = generator.generate_image(
    prompt="美丽的古装少女，温柔笑容",
    image_name="girl_char",
    mode="character"
)
```

### 批量生成

```python
# 为游戏节点生成所有图像
narrative_data = {
    "node_id": "scene_001",
    "narrative_content": {
        "settings": ["古代书房，烛光摇曳"],
        "characters": [
            {
                "name": "李白",
                "description": "诗仙风采，飘逸洒脱"
            }
        ],
        "cg_scenes": ["月下饮酒，诗意盎然"]
    }
}

result = generator.generate_images_for_node(narrative_data)
```

### 角色立绘生成

```python
characters = [
    {
        "name": "林黛玉", 
        "appearance_description": "娇弱美人，柳眉杏眼"
    }
]

results = generator.generate_character_portraits(characters)
```

## 配置说明

### 图像生成配置 (config.ini)

```ini
[generators.image_generation]
output_directory = images
use_cloud = false
```

### RAG配置

RAG增强生成器会自动初始化，默认配置：
- 相似度阈值: 0.2
- IP-Adapter权重: 0.5
- 自动构建索引: True

## API参考

### ImageGenerator类

#### 核心方法

- `generate_image(prompt, image_name, mode)`: 生成单张图像
- `generate_images_for_node(narrative_data)`: 为节点生成所有图像
- `generate_character_portraits(characters)`: 批量生成角色立绘
- `get_generation_status()`: 获取生成器状态

#### 辅助方法

- `_enhance_prompt(prompt, mode)`: 增强提示词
- `_get_dimensions(mode)`: 获取图像尺寸
- `_get_rag_stats()`: 获取RAG统计信息

### 返回值

所有生成方法返回状态字符串：
- `"ok"`: 生成成功
- `"error"`: 生成失败

批量生成方法返回详细结果字典，包含每个图像的生成状态和路径信息。

## 与原版本的区别

### 移除的功能
- 云端/本地切换逻辑
- 外部API依赖
- 复杂的配置管理

### 新增的功能
- RAG检索增强
- 智能提示词工程
- 更好的图像质量
- 统一的生成接口

### 保留的功能
- 所有原有API接口
- 批量生成功能
- 多种生成模式
- 文件保存逻辑

## 依赖要求

- Python 3.8+
- PyTorch
- Transformers
- Diffusers
- PIL/Pillow
- FAISS (用于向量检索)

## 故障排除

### 常见问题

1. **RAG生成器初始化失败**
   - 检查模型文件是否存在
   - 确认GPU内存充足
   - 验证依赖包安装

2. **图像生成失败**
   - 检查输出目录权限
   - 确认提示词格式正确
   - 查看错误日志

3. **检索功能异常**
   - 确认输入图像目录存在
   - 检查索引文件完整性
   - 重建图像索引

### 性能优化

- 使用GPU加速
- 启用混合精度
- 调整批处理大小
- 优化图像索引

## 示例代码

完整的使用示例请参考 `image_generator_example.py` 文件。
