"""
简洁的语音生成器
基于参考音频的语音克隆生成，支持多种音色
"""

import os
import json
import torch
import torchaudio
import numpy as np
from typing import Dict, List, Optional, Union
import sys
from pathlib import Path

# 添加路径以导入其他模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

# 导入统一配置管理
from src.config import get_config, get_section
from src.config.paths import OUTPUT_VOICE_DIR, REFERENCE_AUDIO_DIR

class VocalGenerator:
    """简洁的语音生成器类，基于参考音频进行语音克隆"""

    def __init__(self):
        """初始化语音生成器"""
        # 获取语音生成配置
        self.vocal_config = get_section('generators', 'vocal_generation')

        # 语音输出目录
        self.audio_directory = OUTPUT_VOICE_DIR
        os.makedirs(self.audio_directory, exist_ok=True)

        # 参考音频目录
        self.reference_audio_dir = REFERENCE_AUDIO_DIR

        # 加载音色配置
        self.voice_config = self._load_voice_config()

        # 初始化语音合成模型（这里模拟，实际需要根据具体模型调整）
        self.model = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        print(f"语音生成器初始化完成")
        print(f"可用音色数量: {len(self.voice_config.get('voices', []))}")
        print(f"设备: {self.device}")

    def _load_voice_config(self) -> Dict:
        """加载音色配置文件"""
        config_path = os.path.join(self.reference_audio_dir, 'voice_config.json')
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载音色配置失败: {e}")
            return {"voices": [], "default_voice": "001"}

    def _get_voice_info(self, voice_id: str) -> Optional[Dict]:
        """根据音色ID获取音色信息"""
        voices = self.voice_config.get('voices', [])
        for voice in voices:
            if voice.get('voice_id') == voice_id:
                return voice
        return None

    def _get_reference_audio_path(self, voice_id: str) -> Optional[str]:
        """获取参考音频路径"""
        voice_info = self._get_voice_info(voice_id)
        if voice_info and 'prompt_audio_path' in voice_info:
            audio_path = voice_info['prompt_audio_path']
            if os.path.exists(audio_path):
                return audio_path
        return None

    def _get_prompt_text(self, voice_id: str) -> Optional[str]:
        """获取提示文本"""
        voice_info = self._get_voice_info(voice_id)
        if voice_info and 'prompt_text' in voice_info:
            return voice_info['prompt_text']
        return None

    def generate_voice(self, content: str, speaker: Union[int, str], output_name: str) -> str:
        """
        生成单段语音

        Args:
            content: 要合成的文本内容
            speaker: 说话人ID或名称
            output_name: 输出音频文件名（不含扩展名）

        Returns:
            生成结果状态："ok"表示成功，"error"表示失败
        """
        try:
            # 转换speaker为voice_id
            voice_id = self._convert_speaker_to_voice_id(speaker)

            # 获取参考音频路径和提示文本
            reference_audio_path = self._get_reference_audio_path(voice_id)
            prompt_text = self._get_prompt_text(voice_id)

            if not reference_audio_path or not prompt_text:
                print(f"未找到音色 {voice_id} 的参考音频或提示文本")
                return "error"

            print(f"生成语音: {output_name} (音色: {voice_id})")

            # 执行GPT-SoVITS推理
            success = self._gpt_sovits_inference(
                text=content,
                prompt_text=prompt_text,
                prompt_audio=reference_audio_path,
                output_path=os.path.join(self.audio_directory, f"{output_name}.wav")
            )

            return "ok" if success else "error"

        except Exception as e:
            print(f"语音生成失败: {e}")
            return "error"

    def _gpt_sovits_inference(self, text: str, prompt_text: str, prompt_audio: str, output_path: str) -> bool:
        """
        执行GPT-SoVITS推理

        Args:
            text: 要合成的文本
            prompt_text: 提示文本
            prompt_audio: 提示音频路径
            output_path: 输出音频路径

        Returns:
            是否成功
        """
        try:
            print(f"GPT-SoVITS推理:")
            print(f"  目标文本: {text}")
            print(f"  提示文本: {prompt_text}")
            print(f"  提示音频: {prompt_audio}")
            print(f"  输出路径: {output_path}")

            # 检查提示音频是否存在
            if not os.path.exists(prompt_audio):
                print(f"提示音频文件不存在: {prompt_audio}")
                return False

            # 这里实现GPT-SoVITS的推理逻辑
            # 实际实现需要调用GPT-SoVITS模型

            # 1. 预处理文本
            processed_text = self._preprocess_text(text)

            # 2. 执行推理（模拟）
            success = self._execute_gpt_sovits_model(
                target_text=processed_text,
                prompt_text=prompt_text,
                prompt_audio=prompt_audio,
                output_path=output_path
            )

            if success:
                print(f"GPT-SoVITS推理完成: {output_path}")
                return True

            return False

        except Exception as e:
            print(f"GPT-SoVITS推理失败: {e}")
            return False

    def _convert_speaker_to_voice_id(self, speaker: Union[int, str]) -> str:
        """
        将说话人转换为音色ID

        Args:
            speaker: 说话人ID或名称

        Returns:
            音色ID
        """
        if isinstance(speaker, str):
            # 如果已经是音色ID格式，直接返回
            if speaker.isdigit() or speaker in [v['voice_id'] for v in self.voice_config.get('voices', [])]:
                return speaker

            # 从配置文件获取角色名称映射
            character_voice_mapping = self.vocal_config.get('character_voice_mapping', {
                "narrator": "006",      # 旁白
                "林小满": "002",        # 活泼女声
                "祖母": "005",          # 老者声音
                "村民": "003",          # 成熟男声
                "少年": "004",          # 少年男声
                "女性角色": "001",      # 温柔女声
                "男性角色": "003"       # 成熟男声
            })

            return character_voice_mapping.get(speaker, self.vocal_config.get('default_voice', '001'))

        # 如果是数字，从配置文件获取映射
        voice_id_mapping = self.vocal_config.get('voice_id_mapping', {
            "1": "001", "2": "002", "3": "003",
            "4": "004", "5": "005", "6": "006"
        })

        return voice_id_mapping.get(str(speaker), self.vocal_config.get('default_voice', '001'))

    def _execute_gpt_sovits_model(self, target_text: str, prompt_text: str, prompt_audio: str, output_path: str) -> bool:
        """
        执行GPT-SoVITS模型推理

        Args:
            target_text: 目标文本
            prompt_text: 提示文本
            prompt_audio: 提示音频路径
            output_path: 输出路径

        Returns:
            是否成功
        """
        try:
            # 这里是GPT-SoVITS的实际推理代码
            # 需要根据具体的GPT-SoVITS实现来调整

            # 示例推理流程（需要替换为实际的GPT-SoVITS调用）:
            # 1. 加载模型
            # 2. 处理提示音频和文本
            # 3. 生成目标语音
            # 4. 保存结果

            print(f"执行GPT-SoVITS模型推理...")

            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # 模拟推理过程（实际需要替换为GPT-SoVITS调用）
            # 这里可以调用GPT-SoVITS的API或直接调用模型

            # 生成模拟音频文件
            sample_rate = 22050
            duration = len(target_text) * 0.15  # 估算时长
            samples = int(sample_rate * duration)
            audio_data = np.random.randn(samples).astype(np.float32) * 0.1

            # 保存音频
            audio_tensor = torch.from_numpy(audio_data).unsqueeze(0)
            torchaudio.save(output_path, audio_tensor, sample_rate)

            return True

        except Exception as e:
            print(f"GPT-SoVITS模型执行失败: {e}")
            return False

    def _preprocess_text(self, text: str) -> str:
        """文本预处理"""
        # 清理文本，移除特殊字符等
        processed = text.strip()
        # 可以添加更多预处理逻辑，如标点符号处理等
        return processed

    def get_available_voices(self) -> List[Dict]:
        """获取可用的音色列表"""
        return self.voice_config.get('voices', [])

    def generate_audio_for_node(self, narrative_data: Dict) -> Dict:
        """
        为单个节点生成所需的所有语音
        
        Args:
            narrative_data: 节点叙事数据，包含对话、旁白等
            
        Returns:
            包含生成语音信息的字典
        """
        node_id = narrative_data.get("node_id", "unknown")
        narrative_content = narrative_data.get("narrative_content", {})
        
        result = {
            "node_id": node_id,
            "voice_files": [],
            "music_files": [],
            "sound_files": [],
            "generation_status": "success"
        }
        
        try:
            # 生成对话语音
            dialogues = narrative_content.get("dialogues", [])
            for i, dialogue in enumerate(dialogues):
                character_name = dialogue.get("character", "narrator")
                content = dialogue.get("content", "")
                
                if content.strip():
                    voice_name = f"voice_{node_id}_{character_name}_{i+1}"
                    speaker_id = self._get_speaker_for_character(character_name)
                    status = self.generate_voice(content, speaker_id, voice_name)
                    
                    result["voice_files"].append({
                        "character": character_name,
                        "content": content,
                        "file_name": voice_name,
                        "file_path": f"audio/{voice_name}.wav",
                        "status": status
                    })
            
            # 生成旁白语音
            narrations = narrative_content.get("narrations", [])
            for i, narration in enumerate(narrations):
                if narration.strip():
                    narrator_voice_name = f"narrator_{node_id}_{i+1}"
                    status = self.generate_voice(narration, "narrator", narrator_voice_name)
                    
                    result["voice_files"].append({
                        "character": "narrator",
                        "content": narration,
                        "file_name": narrator_voice_name,
                        "file_path": f"audio/{narrator_voice_name}.wav",
                        "status": status
                    })
            
            # 处理音乐指令（这里只记录，实际音乐生成由music_generator处理）
            music_instructions = narrative_content.get("music_instructions", [])
            for music_inst in music_instructions:
                result["music_files"].append({
                    "instruction": music_inst,
                    "status": "pending"  # 待音乐生成器处理
                })
                
        except Exception as e:
            print(f"节点 {node_id} 语音生成失败: {e}")
            result["generation_status"] = "error"
            result["error_message"] = str(e)
        
        return result
    
    def generate_character_voices(self, characters: List[Dict], sample_texts: List[str]) -> List[Dict]:
        """
        为角色生成示例语音
        
        Args:
            characters: 角色信息列表
            sample_texts: 示例文本列表
            
        Returns:
            生成结果列表
        """
        results = []
        
        for char_info in characters:
            char_name = char_info.get("name", "unknown")
            
            for i, sample_text in enumerate(sample_texts):
                voice_name = f"sample_{char_name}_{i+1}"
                speaker_id = self._get_speaker_for_character(char_name)
                status = self.generate_voice(sample_text, speaker_id, voice_name)
                
                results.append({
                    "character_name": char_name,
                    "sample_text": sample_text,
                    "file_name": voice_name,
                    "file_path": f"audio/{voice_name}.wav",
                    "status": status
                })
        
        return results
    
    def _get_speaker_for_character(self, character_name: str) -> str:
        """
        根据角色名称获取对应的音色ID

        Args:
            character_name: 角色名称

        Returns:
            音色ID
        """
        # 角色到音色的映射
        character_voice_mapping = {
            "林小满": "002",      # 活泼女声
            "祖母": "005",        # 老者声音
            "村民": "003",        # 成熟男声
            "narrator": "006",    # 旁白声音
            "少年": "004",        # 少年男声
            "女性角色": "001",    # 温柔女声
            "男性角色": "003"     # 成熟男声
        }

        return character_voice_mapping.get(character_name, self.voice_config.get('default_voice', '001'))
    
    def get_generation_status(self) -> Dict:
        """
        获取生成器状态信息

        Returns:
            包含状态信息的字典
        """
        return {
            "mode": "gpt_sovits_local",
            "device": str(self.device),
            "output_directory": self.audio_directory,
            "reference_audio_dir": self.reference_audio_dir,
            "available_voices": len(self.voice_config.get('voices', [])),
            "available": self._check_availability()
        }

    def _check_availability(self) -> bool:
        """
        检查生成器可用性

        Returns:
            True表示可用，False表示不可用
        """
        try:
            # 检查参考音频目录是否存在
            if not os.path.exists(self.reference_audio_dir):
                return False

            # 检查是否有可用的音色配置
            voices = self.voice_config.get('voices', [])
            if not voices:
                return False

            # 检查至少有一个参考音频文件存在
            for voice in voices:
                audio_path = voice.get('prompt_audio_path', '')
                if os.path.exists(audio_path):
                    return True

            return False
        except:
            return False


# 为了保持向后兼容性，提供便捷函数
def generate_voice_unified(content: str, speaker: Union[int, str], output_name: str) -> str:
    """
    统一的语音生成函数（向后兼容）
    
    Args:
        content: 要合成的文本内容
        speaker: 说话人ID或名称
        output_name: 输出音频文件名
        
    Returns:
        生成结果状态
    """
    generator = VocalGenerator()
    return generator.generate_voice(content, speaker, output_name)


if __name__ == "__main__":
    # 测试代码
    generator = VocalGenerator()
    print(f"生成器状态: {generator.get_generation_status()}")

    # 显示可用音色
    voices = generator.get_available_voices()
    print(f"\n可用音色:")
    for voice in voices:
        print(f"  {voice['voice_id']}: {voice['voice_name']} - {voice['description']}")

    # 测试生成
    test_result = generator.generate_voice(
        "这是一个测试语音，用来验证语音合成功能是否正常工作。",
        "001",  # 使用温柔女声
        "test_voice"
    )
    print(f"\n测试生成结果: {test_result}")

    # 测试角色映射
    test_result2 = generator.generate_voice(
        "大家好，我是林小满。",
        "林小满",  # 使用角色名称
        "test_character_voice"
    )
    print(f"角色语音生成结果: {test_result2}")
