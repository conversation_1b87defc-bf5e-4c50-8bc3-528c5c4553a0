"""
GPT-SoVITS语音生成器使用示例
展示如何使用基于提示音频和文本的语音合成功能
"""

from .vocal_generator import VocalGenerator


def test_basic_generation():
    """测试基础语音生成"""
    print("=== 基础语音生成测试 ===")
    
    generator = VocalGenerator()
    
    # 检查状态
    status = generator.get_generation_status()
    print(f"生成器状态: {status}")
    
    if not status["available"]:
        print("生成器不可用，请检查参考音频配置")
        return
    
    # 显示可用音色
    voices = generator.get_available_voices()
    print(f"\n可用音色 ({len(voices)} 个):")
    for voice in voices:
        print(f"  {voice['voice_id']}: {voice['voice_name']} - {voice['description']}")
    
    # 测试不同音色
    test_texts = [
        ("这是温柔女声的测试语音。", "001", "test_gentle_female"),
        ("大家好！我是活泼的声音！", "002", "test_lively_female"), 
        ("我是成熟稳重的男性声音。", "003", "test_mature_male"),
        ("你好，我是年轻的男孩声音。", "004", "test_young_male"),
        ("孩子们，听爷爷讲个故事。", "005", "test_elder"),
        ("这里是故事的旁白部分。", "006", "test_narrator")
    ]
    
    print(f"\n开始生成测试语音...")
    for text, voice_id, filename in test_texts:
        print(f"\n生成: {filename} (音色: {voice_id})")
        result = generator.generate_voice(text, voice_id, filename)
        print(f"结果: {result}")


def test_character_mapping():
    """测试角色名称映射"""
    print("\n=== 角色名称映射测试 ===")
    
    generator = VocalGenerator()
    
    # 测试角色名称到音色的映射
    character_tests = [
        ("大家好，我是林小满！", "林小满", "char_linxiaoman"),
        ("孩子，过来听祖母说话。", "祖母", "char_grandmother"),
        ("各位村民请注意！", "村民", "char_villager"),
        ("这是故事的开始...", "narrator", "char_narrator")
    ]
    
    print(f"测试角色语音生成...")
    for text, character, filename in character_tests:
        print(f"\n角色: {character}")
        print(f"台词: {text}")
        result = generator.generate_voice(text, character, filename)
        print(f"生成结果: {result}")


def test_node_generation():
    """测试节点批量生成"""
    print("\n=== 节点批量生成测试 ===")
    
    generator = VocalGenerator()
    
    # 模拟游戏节点数据
    narrative_data = {
        "node_id": "scene_001",
        "narrative_content": {
            "dialogues": [
                {
                    "character": "林小满",
                    "content": "祖母，今天的天气真好呢！"
                },
                {
                    "character": "祖母", 
                    "content": "是啊，小满。这样的好天气适合出去走走。"
                },
                {
                    "character": "村民",
                    "content": "林家的小满真是个好孩子。"
                }
            ],
            "narrations": [
                "阳光透过窗户洒进屋内，温暖而明亮。",
                "祖母慈祥地看着小满，眼中满含着爱意。"
            ]
        }
    }
    
    print("为节点生成所有语音...")
    result = generator.generate_audio_for_node(narrative_data)
    
    print(f"\n节点 {result['node_id']} 生成结果:")
    print(f"状态: {result['generation_status']}")
    print(f"语音文件数量: {len(result['voice_files'])}")
    
    for voice_file in result['voice_files']:
        print(f"  {voice_file['character']}: {voice_file['file_name']} - {voice_file['status']}")


def test_character_voices():
    """测试角色语音样本生成"""
    print("\n=== 角色语音样本测试 ===")
    
    generator = VocalGenerator()
    
    # 角色信息
    characters = [
        {"name": "林小满"},
        {"name": "祖母"},
        {"name": "村民"}
    ]
    
    # 示例文本
    sample_texts = [
        "你好，很高兴认识你。",
        "今天的天气真不错。",
        "我们一起去看看吧。"
    ]
    
    print("生成角色语音样本...")
    results = generator.generate_character_voices(characters, sample_texts)
    
    print(f"\n生成了 {len(results)} 个语音样本:")
    for result in results:
        print(f"  {result['character_name']}: {result['file_name']} - {result['status']}")


def test_voice_info():
    """测试音色信息查询"""
    print("\n=== 音色信息查询测试 ===")
    
    generator = VocalGenerator()
    
    # 测试音色信息获取
    test_voice_ids = ["001", "002", "003", "999"]  # 包含一个不存在的ID
    
    for voice_id in test_voice_ids:
        voice_info = generator._get_voice_info(voice_id)
        if voice_info:
            print(f"音色 {voice_id}: {voice_info['voice_name']}")
            print(f"  描述: {voice_info['description']}")
            print(f"  性别: {voice_info['gender']}")
            print(f"  年龄: {voice_info['age_range']}")
            
            # 检查参考音频路径
            audio_path = generator._get_reference_audio_path(voice_id)
            print(f"  音频路径: {audio_path}")
            print(f"  文件存在: {audio_path and os.path.exists(audio_path)}")
        else:
            print(f"音色 {voice_id}: 未找到")
        print()


def main():
    """运行所有测试"""
    try:
        test_basic_generation()
        test_character_mapping()
        test_node_generation()
        test_character_voices()
        test_voice_info()
        
        print("\n=== 所有测试完成 ===")
        
    except Exception as e:
        print(f"测试运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    import os
    main()
