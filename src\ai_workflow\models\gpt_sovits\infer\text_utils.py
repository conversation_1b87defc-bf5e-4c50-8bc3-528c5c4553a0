import re
from typing import List, Sequence

tier1_punc = ".?!。？！"
tier2_punc = ",.:;…~-—，、：；"
tier3_punc = "\"'“”‘’（）《》【】[]「」『』<>·"


def cut5(inp: str, splits: Sequence[str]):
    """Cut one line of text into pieces."""
    items = re.split(f"([{''.join(re.escape(x) for x in splits)}])", inp)
    items = [item.strip() for item in items if item.strip()]
    if len(items) % 2 == 1:
        items.append("")
    mergeitems = [a + b for a, b in zip(items[0::2], items[1::2])]
    return mergeitems


def merge_short_texts(texts: List[str], threshold: int = 36):
    """Merge short texts to longer ones. Texts are generated by cut5."""
    result: List[str] = []
    text = ""
    for ele in texts:
        text += ele
        if len(text) >= threshold:
            result.append(text)
            text = ""
    if text:
        result.append(text)
    return result


def clean_and_cut_text(text: str) -> List[str]:
    new_text = ""
    for char in text:
        if char in tier2_punc:
            new_text += ","
        elif char in tier3_punc:
            new_text += " "
        else:
            new_text += char
    text = new_text

    lines = [line.strip() for line in text.split("\n") if line.strip()]
    res: List[str] = []
    for line in lines:
        sents = [sent for sent in cut5(line, tier1_punc)]
        texts = [
            merged.strip()
            for sent in sents
            for merged in merge_short_texts(cut5(sent, ","))
        ]
        texts = merge_short_texts(texts)
        res.extend(texts)
    res = [("。" + x) if x[0] != "。" else x for x in res]
    return res
