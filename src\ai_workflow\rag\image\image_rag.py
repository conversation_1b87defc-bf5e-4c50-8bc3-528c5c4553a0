"""
ImageRAG - 图像检索增强生成的统一接口
直接使用同包内的工具类，提供简洁的图像RAG功能
"""

import os
from typing import Optional, List, Dict, Any, Tuple, Union
from PIL import Image
import logging
from src.config.paths import OUTPUT_IMAGE_DIR, PREPROCESS_IMAGE_DIR

from .clip_image_indexer import CLIPImageIndexer
from .text_image_retriever import TextImageRetriever

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ImageRAG:
    """
    图像检索增强生成统一接口

    直接使用同包内的工具类，提供简洁的图像RAG功能：
    - 基于CLIP的图像检索
    - IP-Adapter增强的图像生成
    - 自动索引管理
    """

    def __init__(
        self,
        similarity_threshold: float = 0.2,
        ip_adapter_scale: float = 0.5,
        auto_build_index: bool = True,
        clip_model_path: Optional[str] = None
    ):
        """
        初始化ImageRAG

        Args:
            similarity_threshold: 图像检索相似度阈值 (0.0-1.0)
            auto_build_index: 是否自动构建图像索引
            clip_model_path: CLIP模型路径，None使用默认路径
        """
        self.similarity_threshold = similarity_threshold
        self.ip_adapter_scale = ip_adapter_scale

        # 设置输出目录
        self.output_dir = OUTPUT_IMAGE_DIR
        os.makedirs(self.output_dir, exist_ok=True)

        # 初始化工具类
        self.indexer = None
        self.retriever = TextImageRetriever(similarity_threshold=similarity_threshold)


        # 自动构建索引
        if auto_build_index:
            self._ensure_index_ready()

        logger.info("ImageRAG初始化完成")

    
    def search_similar_images(
        self,
        text: str,
        top_k: int = 5
    ) -> List[Tuple[str, float]]:
        """
        搜索相似图像

        Args:
            text: 查询文本
            top_k: 返回前k个最相似的图像

        Returns:
            (图像路径, 相似度分数)的列表
        """
        return self.retriever.search_similar_images(text, top_k)

    def find_best_match(self, text: str) -> Optional[Tuple[str, float]]:
        """
        查找最佳匹配图像

        Args:
            text: 查询文本

        Returns:
            最佳匹配的(图像路径, 相似度分数)，无匹配则返回None
        """
        return self.retriever.search_best_match(text)
    
    def build_index(self, image_dir: Optional[str] = None) -> bool:
        """
        构建图像索引

        Args:
            image_dir: 图像目录路径，None使用默认预处理图像目录

        Returns:
            是否成功构建索引
        """
        self.indexer = CLIPImageIndexer()
        return self.indexer.build_index_from_directory(image_dir)
    
    def update_settings(
        self,
        similarity_threshold: Optional[float] = None,
        ip_adapter_scale: Optional[float] = None
    ):
        """
        更新设置

        Args:
            similarity_threshold: 新的相似度阈值
            ip_adapter_scale: 新的IP-Adapter权重
        """
        if similarity_threshold is not None:
            self.similarity_threshold = similarity_threshold
            self.retriever.similarity_threshold = similarity_threshold

        if ip_adapter_scale is not None:
            self.ip_adapter_scale = ip_adapter_scale
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取系统统计信息

        Returns:
            包含检索和生成系统信息的字典
        """
        return {
            "retrieval": self.get_index_info(),
            "generation": {
                "ip_adapter_scale": self.ip_adapter_scale,
                "output_dir": str(self.output_dir)
            },
            "settings": {
                "similarity_threshold": self.similarity_threshold,
                "ip_adapter_scale": self.ip_adapter_scale
            }
        }

    def get_index_info(self) -> Dict[str, Any]:
        """
        获取索引信息

        Returns:
            索引状态和统计信息
        """
        return self.retriever.get_retrieval_stats()

    def _ensure_index_ready(self):
        """确保索引已准备就绪"""
        stats = self.get_index_info()

        if stats.get("total_images", 0) == 0:
            logger.info("未找到现有索引，正在构建索引...")
            if self.build_index():
                logger.info("索引构建成功")
            else:
                logger.warning("索引构建失败")
        else:
            logger.info(f"索引已加载，包含 {stats['total_images']} 张图像")

    def cleanup(self):
        """清理资源"""
        try:
            # 清理检索器模型
            if hasattr(self.retriever, 'model'):
                del self.retriever.model
            if hasattr(self.retriever, 'processor'):
                del self.retriever.processor

            # 清理索引器模型
            if hasattr(self.indexer, 'model'):
                del self.indexer.model
            if hasattr(self.indexer, 'processor'):
                del self.indexer.processor

            logger.info("ImageRAG资源已清理")
        except Exception as e:
            logger.warning(f"清理资源时出错: {e}")

    def __del__(self):
        """析构函数"""
        self.cleanup()
