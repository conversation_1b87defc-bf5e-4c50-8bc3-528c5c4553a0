"""
RAGRetrieval - RAG检索统一接口
提供简洁的API来统一管理文本和图像的RAG功能
"""

from typing import Dict, Any, Optional
import logging

from .text.text_rag import TextRAG
from .image.image_rag import ImageRAG

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RAGRetrieval:
    """
    RAG检索统一接口
    
    统一管理文本RAG和图像RAG功能，提供简洁的对外接口
    """
    
    def __init__(
        self,
        enable_text_rag: bool = True,
        enable_image_rag: bool = True,
        text_rag_config: Optional[Dict[str, Any]] = None,
        image_rag_config: Optional[Dict[str, Any]] = None
    ):
        """
        初始化RAG检索系统
        
        Args:
            enable_text_rag: 是否启用文本RAG
            enable_image_rag: 是否启用图像RAG
            text_rag_config: 文本RAG配置
            image_rag_config: 图像RAG配置
        """
        self.enable_text_rag = enable_text_rag
        self.enable_image_rag = enable_image_rag
        
        # 初始化文本RAG
        self.text_rag = None
        if enable_text_rag:
            try:
                config = text_rag_config or {}
                self.text_rag = TextRAG(**config)
                logger.info("文本RAG初始化成功")
            except Exception as e:
                logger.error(f"文本RAG初始化失败: {e}")
                self.enable_text_rag = False
        
        # 初始化图像RAG
        self.image_rag = None
        if enable_image_rag:
            try:
                config = image_rag_config or {}
                self.image_rag = ImageRAG(**config)
                logger.info("图像RAG初始化成功")
            except Exception as e:
                logger.error(f"图像RAG初始化失败: {e}")
                self.enable_image_rag = False
        
        logger.info("RAGRetrieval初始化完成")
    
    def build_index(self, text_data_dir: Optional[str] = None, image_data_dir: Optional[str] = None) -> Dict[str, Any]:
        """
        构建RAG索引

        Args:
            text_data_dir: 文本数据目录，None时使用默认预处理文本目录
            image_data_dir: 图像数据目录，None时使用默认预处理图像目录

        Returns:
            构建结果字典，包含详细信息
        """
        results = {
            "text_rag": {
                "enabled": self.enable_text_rag,
                "success": False,
                "processed_files": 0,
                "error": None
            },
            "image_rag": {
                "enabled": self.enable_image_rag,
                "success": False,
                "indexed_images": 0,
                "error": None
            },
            "overall_success": False
        }

        # 构建文本索引
        if self.enable_text_rag and self.text_rag:
            try:
                logger.info("开始构建文本RAG索引...")

                # 处理预处理目录中的文本文件
                processed_results = self.text_rag.process_preprocessed_files(text_data_dir)

                # 统计处理结果
                successful_files = [r for r in processed_results if "error" not in r]
                results["text_rag"]["processed_files"] = len(successful_files)
                results["text_rag"]["success"] = len(successful_files) > 0

                if results["text_rag"]["success"]:
                    logger.info(f"文本索引构建成功，处理了 {len(successful_files)} 个文件")
                else:
                    logger.warning("文本索引构建失败，没有成功处理任何文件")

            except Exception as e:
                error_msg = f"文本索引构建失败: {e}"
                logger.error(error_msg)
                results["text_rag"]["error"] = str(e)

        # 构建图像索引
        if self.enable_image_rag and self.image_rag:
            try:
                logger.info("开始构建图像RAG索引...")

                # 构建图像索引
                success = self.image_rag.build_index(image_data_dir)
                results["image_rag"]["success"] = success

                if success:
                    # 获取索引统计信息
                    stats = self.image_rag.get_index_info()
                    results["image_rag"]["indexed_images"] = stats.get("total_images", 0)
                    logger.info(f"图像索引构建成功，索引了 {results['image_rag']['indexed_images']} 张图像")
                else:
                    logger.warning("图像索引构建失败")

            except Exception as e:
                error_msg = f"图像索引构建失败: {e}"
                logger.error(error_msg)
                results["image_rag"]["error"] = str(e)

        # 计算总体成功状态
        text_ok = not self.enable_text_rag or results["text_rag"]["success"]
        image_ok = not self.enable_image_rag or results["image_rag"]["success"]
        results["overall_success"] = text_ok and image_ok

        logger.info(f"RAG索引构建完成，总体状态: {'成功' if results['overall_success'] else '失败'}")
        return results

    def rebuild_index(self, text_data_dir: Optional[str] = None, image_data_dir: Optional[str] = None) -> Dict[str, Any]:
        """
        重建RAG索引（别名方法，保持向后兼容）

        Args:
            text_data_dir: 文本数据目录，None时使用默认预处理文本目录
            image_data_dir: 图像数据目录，None时使用默认预处理图像目录

        Returns:
            构建结果字典
        """
        return self.build_index(text_data_dir, image_data_dir)

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            包含文本和图像RAG统计信息的字典
        """
        stats = {
            "text_rag": {
                "enabled": self.enable_text_rag,
                "status": "未初始化",
                "total_documents": 0,
                "embedding_model": None
            },
            "image_rag": {
                "enabled": self.enable_image_rag,
                "status": "未初始化",
                "total_images": 0,
                "index_loaded": False
            },
            "overall_status": "未初始化"
        }

        # 获取文本RAG统计信息
        if self.enable_text_rag and self.text_rag:
            try:
                # 使用TextRAG的get_stats方法
                text_stats = self.text_rag.get_stats()

                stats["text_rag"].update({
                    "status": "正常",
                    "embedding_model": text_stats.get("embedding_tool", {}).get("model", "unknown"),
                    "use_local": text_stats.get("embedding_tool", {}).get("use_local", False),
                    "retriever_stats": text_stats.get("retriever", {})
                })

                # 尝试获取文档数量
                if hasattr(self.text_rag.retriever, 'get_stats'):
                    retriever_stats = self.text_rag.retriever.get_stats()
                    stats["text_rag"]["total_documents"] = retriever_stats.get("total_vectors", 0)

            except Exception as e:
                stats["text_rag"]["status"] = f"错误: {e}"

        # 获取图像RAG统计信息
        if self.enable_image_rag and self.image_rag:
            try:
                # 使用ImageRAG的get_stats和get_index_info方法
                image_stats = self.image_rag.get_stats()
                index_info = self.image_rag.get_index_info()

                stats["image_rag"].update({
                    "status": "正常",
                    "total_images": index_info.get("total_images", 0),
                    "index_loaded": index_info.get("index_loaded", False),
                    "similarity_threshold": index_info.get("similarity_threshold", 0.0),
                    "index_dir": index_info.get("index_dir", "unknown")
                })

            except Exception as e:
                stats["image_rag"]["status"] = f"错误: {e}"

        # 计算总体状态
        text_ok = not self.enable_text_rag or stats["text_rag"]["status"] == "正常"
        image_ok = not self.enable_image_rag or stats["image_rag"]["status"] == "正常"

        if text_ok and image_ok:
            stats["overall_status"] = "正常"
        elif stats["text_rag"]["status"] == "正常" or stats["image_rag"]["status"] == "正常":
            stats["overall_status"] = "部分正常"
        else:
            stats["overall_status"] = "异常"

        return stats
    
    def search_text(self, dtype:str, query: str, top_k: int = 5):
        """
        文本检索
        
        Args:
            dtype: 查询的库
            query: 查询文本
            top_k: 返回结果数量
            
        Returns:
            检索结果
        """
        if not self.enable_text_rag or not self.text_rag:
            raise RuntimeError("文本RAG未启用或初始化失败")
        
        return self.text_rag.search(dtype,query, top_k)
    
    def search_images(self, query: str, top_k: int = 5):
        """
        图像检索
        
        Args:
            query: 查询文本
            top_k: 返回结果数量
            
        Returns:
            检索结果
        """
        if not self.enable_image_rag or not self.image_rag:
            raise RuntimeError("图像RAG未启用或初始化失败")
        
        return self.image_rag.search_similar_images(query, top_k)