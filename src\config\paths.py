from pathlib import Path

# ========== 项目根目录 ==========
PROJECT_ROOT = Path(__file__).resolve().parents[2]  # 定位到 project_root

# ========== 全局目录 ==========
SRC_DIR = PROJECT_ROOT / "src"
TEMP_DIR = PROJECT_ROOT / "temp"
DATA_DIR = PROJECT_ROOT / "data"
CHECKPOINTS_DIR = PROJECT_ROOT / "checkpoints"
OUTPUT_DIR = PROJECT_ROOT / "output"

# ========== temp: 中间文件 ==========
PREPROCESS_TEXT_DIR = TEMP_DIR / "preprocess" / "text"
PREPROCESS_IMAGE_DIR = TEMP_DIR / "preprocess" / "image"

FRAMEWORK_DIR = TEMP_DIR / "framework"

NODE_TEXT_DIR = TEMP_DIR / "node" / "text"
NODE_IMAGE_DIR = TEMP_DIR / "node" / "image"
NODE_VOICE_DIR = TEMP_DIR / "node" / "voice"
NODE_MUSIC_DIR = TEMP_DIR / "node" / "music"
NODE_SCRIPTS_DIR = TEMP_DIR / "node" / "scripts"

# ========== data: 数据库/检索器 ==========
EMBEDDING_DB_DIR = DATA_DIR / "embedding"
RETRIEVER_RESULTS_DIR = DATA_DIR / "retriever"

# ========== input: 输入数据 ==========
INPUT_DIR = PROJECT_ROOT / "input"
INPUT_TEXT_DIR = INPUT_DIR / "text"
INPUT_IMAGE_DIR = INPUT_DIR / "image"
INPUT_AUDIO_DIR = INPUT_DIR / "audio"
INPUT_MUSIC_DIR = INPUT_DIR / "music"

# ========== models: 模型文件 ==========
MODELS_DIR = SRC_DIR / "ai_workflow" / "models"
GPT_SOVITS_DIR = MODELS_DIR / "gpt_sovits"
REFERENCE_AUDIO_DIR = GPT_SOVITS_DIR / "reference_audio"
CLIP_MODEL_DIR = MODELS_DIR / "clip-vit-base-patch16"
SD_MODEL_DIR = MODELS_DIR / "anything-v5"
EMBEDDING_MODEL_DIR= MODELS_DIR / "Qwen3-Embedding-0.6B"

# ========== rag: 检索增强生成 ==========
RAG_DIR = SRC_DIR / "ai_workflow" / "rag"
RAG_TEXT_DIR = RAG_DIR / "text"
RAG_IMAGE_DIR = RAG_DIR / "image"
RAG_TEXT_INDEX_DIR = EMBEDDING_DB_DIR / "text"
RAG_IMAGE_INDEX_DIR = EMBEDDING_DB_DIR / "image"

# ========== output: 最终结果 ==========
OUTPUT_TEXT_DIR = OUTPUT_DIR / "text"
OUTPUT_IMAGE_DIR = OUTPUT_DIR / "image"
OUTPUT_VOICE_DIR = OUTPUT_DIR / "voice"
OUTPUT_MUSIC_DIR = OUTPUT_DIR / "music"
OUTPUT_SCRIPTS_DIR = OUTPUT_DIR / "scripts"

# ========== 确保目录存在 ==========
ALL_DIRS = [
    # temp目录
    PREPROCESS_TEXT_DIR, PREPROCESS_IMAGE_DIR,
    FRAMEWORK_DIR,
    NODE_TEXT_DIR, NODE_IMAGE_DIR, NODE_VOICE_DIR, NODE_MUSIC_DIR, NODE_SCRIPTS_DIR,
    # data目录
    EMBEDDING_DB_DIR, RETRIEVER_RESULTS_DIR,
    # input目录
    INPUT_TEXT_DIR, INPUT_IMAGE_DIR, INPUT_AUDIO_DIR, INPUT_MUSIC_DIR,
    # models目录
    GPT_SOVITS_DIR, REFERENCE_AUDIO_DIR, CLIP_MODEL_DIR, SD_MODEL_DIR,
    # rag目录
    RAG_TEXT_INDEX_DIR, RAG_IMAGE_INDEX_DIR,
    # output目录
    OUTPUT_TEXT_DIR, OUTPUT_IMAGE_DIR, OUTPUT_VOICE_DIR, OUTPUT_MUSIC_DIR, OUTPUT_SCRIPTS_DIR,
    # checkpoints目录
    CHECKPOINTS_DIR,
]

for d in ALL_DIRS:
    d.mkdir(parents=True, exist_ok=True)
