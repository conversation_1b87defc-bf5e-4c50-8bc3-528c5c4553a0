# ImageRAG API 文档

`ImageRAG` 是一个简洁的统一接口，封装了基于CLIP与IP-Adapter的图像检索增强生成功能。

## 快速开始

```python
from src.ai_workflow.rag.image import ImageRAG

# 初始化
rag = ImageRAG()

# 生成图像
image, info = rag.generate("传统中国花纹，精美刺绣")
```

## 类初始化

### `ImageRAG(similarity_threshold=0.2, ip_adapter_scale=0.5, auto_build_index=True, clip_model_path=None, sd_model_path=None)`

**参数:**
- `similarity_threshold` (float): 图像检索相似度阈值，范围0.0-1.0，默认0.2
- `ip_adapter_scale` (float): IP-Adapter影响权重，范围0.0-1.0，默认0.5
- `auto_build_index` (bool): 是否自动构建图像索引，默认True
- `clip_model_path` (str, optional): CLIP模型路径，None使用默认路径
- `sd_model_path` (str, optional): Stable Diffusion模型路径，None使用默认路径

## 核心方法

### 1. 图像生成

#### `generate(prompt, **kwargs) -> Tuple[Image.Image, Dict[str, Any]]`

生成单张图像。

**参数:**
- `prompt` (str): 文本提示词
- `negative_prompt` (str, optional): 负面提示词
- `width` (int): 图像宽度，默认512
- `height` (int): 图像高度，默认768
- `steps` (int): 推理步数，默认25
- `guidance_scale` (float): 引导强度，默认7.5
- `seed` (int, optional): 随机种子
- `use_retrieval` (bool): 是否使用图像检索增强，默认True
- `save` (bool): 是否保存生成的图像，默认True
- `filename_prefix` (str, optional): 保存文件名前缀

**返回:**
- `Tuple[Image.Image, Dict[str, Any]]`: (生成的图像, 生成信息字典)

**生成信息字典包含:**
- `text_prompt`: 使用的文本提示
- `use_image_enhancement`: 是否使用了图像增强
- `retrieved_image`: 检索到的参考图像路径（如果有）
- `similarity_score`: 相似度分数（如果有）
- `generation_time`: 生成耗时（秒）
- `output_path`: 保存路径（如果保存）

#### `batch_generate(prompts, **kwargs) -> List[Tuple[Image.Image, Dict[str, Any]]]`

批量生成多张图像。

**参数:**
- `prompts` (List[str]): 文本提示词列表
- `**kwargs`: 其他生成参数，参考`generate`方法

**返回:**
- `List[Tuple[Image.Image, Dict[str, Any]]]`: 生成结果列表

### 2. 图像检索

#### `search_similar_images(text, top_k=5) -> List[Tuple[str, float]]`

搜索相似图像。

**参数:**
- `text` (str): 查询文本
- `top_k` (int): 返回前k个最相似的图像，默认5

**返回:**
- `List[Tuple[str, float]]`: (图像路径, 相似度分数)的列表

#### `find_best_match(text) -> Optional[Tuple[str, float]]`

查找最佳匹配图像。

**参数:**
- `text` (str): 查询文本

**返回:**
- `Optional[Tuple[str, float]]`: 最佳匹配的(图像路径, 相似度分数)，无匹配则返回None

### 3. 索引管理

#### `rebuild_index(image_dir=None) -> bool`

重建图像索引。

**参数:**
- `image_dir` (str, optional): 图像目录路径，None使用默认输入目录

**返回:**
- `bool`: 是否成功重建索引

### 4. 设置管理

#### `update_settings(similarity_threshold=None, ip_adapter_scale=None)`

更新设置。

**参数:**
- `similarity_threshold` (float, optional): 新的相似度阈值
- `ip_adapter_scale` (float, optional): 新的IP-Adapter权重

### 5. 信息查询

#### `get_stats() -> Dict[str, Any]`

获取系统统计信息。

**返回:**
- `Dict[str, Any]`: 包含检索和生成系统信息的字典

#### `get_index_info() -> Dict[str, Any]`

获取索引信息。

**返回:**
- `Dict[str, Any]`: 索引状态和统计信息

## 属性

### `similarity_threshold` (float)
当前相似度阈值，可读写。

### `ip_adapter_scale` (float)
当前IP-Adapter权重，可读写。

## 使用示例

### 基础使用

```python
# 初始化
rag = ImageRAG(similarity_threshold=0.2, ip_adapter_scale=0.5)

# 生成图像
image, info = rag.generate(
    prompt="传统中国花纹，精美刺绣，高质量",
    width=512,
    height=768,
    steps=25
)

print(f"生成时间: {info['generation_time']:.2f}秒")
if info['retrieved_image']:
    print(f"参考图像: {info['retrieved_image']}")
```

### 图像搜索

```python
# 搜索相似图像
similar_images = rag.search_similar_images("传统花纹", top_k=5)
for path, score in similar_images:
    print(f"{path}: {score:.3f}")

# 查找最佳匹配
best_match = rag.find_best_match("古代建筑")
if best_match:
    path, score = best_match
    print(f"最佳匹配: {path} (相似度: {score:.3f})")
```

### 批量生成

```python
prompts = [
    "传统中国花纹，红色背景",
    "古代宫殿建筑，金色装饰",
    "山水画风景，水墨画风格"
]

results = rag.batch_generate(prompts, width=512, height=512, steps=20)
for i, (image, info) in enumerate(results):
    print(f"图像 {i+1}: {info['output_path']}")
```

### 设置调整

```python
# 查看当前设置
print(f"相似度阈值: {rag.similarity_threshold}")
print(f"IP-Adapter权重: {rag.ip_adapter_scale}")

# 更新设置
rag.update_settings(similarity_threshold=0.3, ip_adapter_scale=0.7)

# 或通过属性设置
rag.similarity_threshold = 0.25
rag.ip_adapter_scale = 0.4
```

### 对比生成

```python
prompt = "传统中国花纹"

# 纯文本生成
image1, info1 = rag.generate(prompt, use_retrieval=False)

# 检索增强生成
image2, info2 = rag.generate(prompt, use_retrieval=True)

print(f"纯文本生成时间: {info1['generation_time']:.2f}秒")
print(f"增强生成时间: {info2['generation_time']:.2f}秒")
```

## 注意事项

1. **首次使用**: 首次运行时会自动构建图像索引，可能需要一些时间
2. **模型路径**: 确保CLIP和Stable Diffusion模型文件在正确位置
3. **内存使用**: 图像生成需要较多GPU内存，建议使用CUDA
4. **相似度阈值**: 较低的阈值会找到更多匹配，但可能相关性较低
5. **IP-Adapter权重**: 较高的权重会让生成图像更接近参考图像

## 错误处理

所有方法都会抛出相应的异常，建议使用try-catch进行错误处理：

```python
try:
    image, info = rag.generate("测试提示词")
except Exception as e:
    print(f"生成失败: {e}")
```
