"""
图像预处理器
将图像拷贝到统一目录并重命名
"""

import os
import shutil
from typing import List
import logging
from src.config.paths import INPUT_IMAGE_DIR, PREPROCESS_IMAGE_DIR

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ImagePreprocessor:
    """图像预处理器 - 将图像拷贝到统一目录并重命名"""

    def __init__(self):
        """初始化图像预处理器"""
        # 设置输入输出目录
        self.input_dir = INPUT_IMAGE_DIR
        self.output_dir = PREPROCESS_IMAGE_DIR

        # 创建目录
        os.makedirs(self.input_dir, exist_ok=True)
        os.makedirs(self.output_dir, exist_ok=True)

        # 支持的图像格式
        self.supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.gif', '.webp'}

        logger.info(f"Image preprocessor initialized")
        logger.info(f"Input directory: {self.input_dir}")

    def is_image_file(self, file_path: str) -> bool:
        """检查文件是否为支持的图像格式"""
        return os.path.splitext(file_path)[1].lower() in self.supported_formats

    def copy_and_rename_image(self, source_path: str, index: int) -> str:
        """拷贝图像到输入目录并重命名"""
        if not self.is_image_file(source_path):
            raise ValueError(f"Unsupported image format: {source_path}")

        # 获取文件扩展名
        _, ext = os.path.splitext(source_path)

        # 生成新文件名
        new_filename = f"image_{index:04d}{ext.lower()}"
        dest_path = os.path.join(self.input_dir, new_filename)

        # 拷贝文件
        shutil.copy2(source_path, dest_path)
        logger.info(f"Copied {os.path.basename(source_path)} -> {new_filename}")

        return dest_path

    def process_images(self, image_paths: List[str]) -> List[str]:
        """批量处理图像"""
        processed_paths = []

        for i, image_path in enumerate(image_paths):
            if not os.path.exists(image_path):
                logger.warning(f"Image file not found: {image_path}")
                continue

            if not self.is_image_file(image_path):
                logger.warning(f"Unsupported image format: {image_path}")
                continue

            try:
                dest_path = self.copy_and_rename_image(image_path, i + 1)
                processed_paths.append(dest_path)
            except Exception as e:
                logger.error(f"Failed to process image {image_path}: {e}")

        logger.info(f"Processed {len(processed_paths)} images")
        return processed_paths

    def process_directory(self, source_dir: str) -> List[str]:
        """处理目录中的所有图像"""
        if not os.path.exists(source_dir):
            logger.error(f"Source directory not found: {source_dir}")
            return []

        # 收集所有图像文件
        image_files = []
        for root, _, files in os.walk(source_dir):
            for file in files:
                file_path = os.path.join(root, file)
                if self.is_image_file(file_path):
                    image_files.append(file_path)

        logger.info(f"Found {len(image_files)} image files in {source_dir}")
        return self.process_images(image_files)
