"""
故事灵感勘探器类
封装调用大语言模型进行故事灵感勘探的功能
"""

import logging
import json
from pathlib import Path
from typing import Dict, Any, Optional, List
import time

# 导入项目内的统一GPT客户端
from src.ai_workflow.utils.gpt_client import get_gpt_client
# 导入故事灵感勘探提示词
from .prompt.summary_prompt import SUMMARY_GENERATION_PROMPTS, STORY_INSPIRATION_EXTRACTOR_PROMPT


class Summarizer:
    """
    故事灵感勘探器类，使用项目统一的GPT客户端进行故事灵感勘探和摘要生成
    """

    def __init__(self, output_dir: str = "summaries"):
        """
        初始化故事灵感勘探器

        Args:
            output_dir: 输出文件保存目录
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)

        # 设置日志
        self.logger = logging.getLogger(__name__)

        # 获取统一的GPT客户端
        self.gpt_client = get_gpt_client()

        # 加载提示词
        self.prompts = SUMMARY_GENERATION_PROMPTS

        self.logger.info("故事灵感勘探器初始化完成，使用统一GPT客户端")



    def _parse_inspiration_response(self, response: str) -> Dict[str, Any]:
        """解析故事灵感勘探的JSON响应"""
        try:
            # 尝试直接解析JSON
            inspiration = json.loads(response.strip())

            # 验证必要的字段
            required_fields = ["world_premises", "dramatic_cores", "character_archetypes",
                             "symbolic_resonances", "narrative_hooks"]

            for field in required_fields:
                if field not in inspiration:
                    inspiration[field] = []
                elif not isinstance(inspiration[field], list):
                    inspiration[field] = []

            return inspiration

        except json.JSONDecodeError as e:
            self.logger.warning(f"JSON解析失败，使用文本解析: {e}")
            # 后备文本解析方案
            return self._parse_inspiration_text_fallback(response)

    def _parse_inspiration_text_fallback(self, response: str) -> Dict[str, Any]:
        """故事灵感勘探的文本解析后备方案"""
        inspiration = {
            "world_premises": [],
            "dramatic_cores": [],
            "character_archetypes": [],
            "symbolic_resonances": [],
            "narrative_hooks": []
        }

        lines = response.split('\n')
        current_category = None

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 检测类别标题
            if "world_premises" in line.lower() or "世界前提" in line:
                current_category = "world_premises"
            elif "dramatic_cores" in line.lower() or "戏剧核心" in line:
                current_category = "dramatic_cores"
            elif "character_archetypes" in line.lower() or "角色原型" in line:
                current_category = "character_archetypes"
            elif "symbolic_resonances" in line.lower() or "象征共鸣" in line:
                current_category = "symbolic_resonances"
            elif "narrative_hooks" in line.lower() or "叙事钩子" in line:
                current_category = "narrative_hooks"
            elif current_category and (line.startswith('-') or line.startswith('•')):
                # 提取列表项
                item = line.lstrip('-•').strip()
                if item:
                    inspiration[current_category].append(item)

        return inspiration

    def summarize(self, text: str, prompt_type: str = "story_inspiration",
                 custom_prompt: Optional[str] = None) -> Dict[str, Any]:
        """
        从文本中勘探故事灵感（原summarize方法的新实现）

        Args:
            text: 要分析的文本
            prompt_type: 提示词类型，默认为"story_inspiration"
            custom_prompt: 自定义提示词（优先级最高）

        Returns:
            包含故事灵感的字典
        """
        if not text.strip():
            raise ValueError("输入文本不能为空")

        try:
            # 使用故事灵感勘探提示词
            system_prompt = STORY_INSPIRATION_EXTRACTOR_PROMPT["system"]
            user_prompt = STORY_INSPIRATION_EXTRACTOR_PROMPT["user"].format(text=text)

            # 调用统一的GPT客户端
            response = self.gpt_client.chat(
                system=system_prompt,
                prompt=user_prompt
            )

            # 解析JSON响应
            inspiration_data = self._parse_inspiration_response(response)

            # 添加元数据
            inspiration_data["metadata"] = {
                'original_length': len(text),
                'response_length': len(response),
                'timestamp': time.time(),
                'extraction_type': 'story_inspiration'
            }

            self.logger.info(f"故事灵感勘探完成，原文{len(text)}字符")
            return inspiration_data

        except Exception as e:
            self.logger.error(f"故事灵感勘探失败: {str(e)}")
            raise

    def summarize_segments(self, segments: List[Dict[str, Any]],
                          source_name: str, prompt_type: str = "story_inspiration") -> List[Dict[str, Any]]:
        """
        批量从章节中勘探故事灵感

        Args:
            segments: 章节列表，每个元素应包含 'text' 字段
            source_name: 源文件名
            prompt_type: 提示词类型

        Returns:
            包含故事灵感的章节列表
        """
        inspiration_segments = []

        for i, segment in enumerate(segments):
            try:
                # 检查段落是否包含文本
                if 'text' not in segment or not segment['text'].strip():
                    self.logger.warning(f"章节{i+1}缺少文本内容，跳过灵感勘探")
                    inspiration_segments.append(segment)
                    continue

                # 勘探故事灵感
                inspiration_result = self.summarize(segment['text'], prompt_type=prompt_type)

                # 更新段落信息
                segment_with_inspiration = segment.copy()
                segment_with_inspiration.update(inspiration_result)

                # 保存灵感到文件
                inspiration_file = self.output_dir / f"{source_name}_seg{i+1}_inspiration.json"
                with open(inspiration_file, 'w', encoding='utf-8') as f:
                    json.dump(inspiration_result, f, ensure_ascii=False, indent=2)

                segment_with_inspiration['inspiration_file'] = str(inspiration_file)
                inspiration_segments.append(segment_with_inspiration)

                self.logger.info(f"章节{i+1}故事灵感勘探完成")

                # 添加延迟避免API限制
                time.sleep(1)

            except Exception as e:
                self.logger.error(f"章节{i+1}故事灵感勘探失败: {str(e)}")
                # 即使失败也保留原始段落
                inspiration_segments.append(segment)

        # 保存批量灵感元数据
        metadata_file = self.output_dir / f"{source_name}_inspirations_metadata.json"
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(inspiration_segments, f, ensure_ascii=False, indent=2)

        self.logger.info(f"批量故事灵感勘探完成: {source_name}, 共{len(inspiration_segments)}个章节")
        return inspiration_segments

    def aggregate_inspirations(self, analyses_list: List[Dict]) -> Dict:
        """聚合多个灵感分析结果"""
        aggregated = {
            "world_premises": [],
            "dramatic_cores": [],
            "character_archetypes": [],
            "symbolic_resonances": [],
            "narrative_hooks": []
        }

        for i, analysis in enumerate(analyses_list):
            for category in aggregated.keys():
                items = analysis.get(category, [])
                if isinstance(items, list):
                    for item in items:
                        aggregated[category].append({
                            "content": item,
                            "source_id": f"material_{i+1}",
                            "unique_id": f"{category}_{i}_{hash(str(item))}",
                            "weight": self._calculate_relevance(item, category)
                        })

        # 后处理：去重、排序、质量过滤
        return self._process_aggregated_data(aggregated)

    def _calculate_relevance(self, item: str, category: str) -> float:
        """计算灵感项目的相关性评分（简单实现）"""
        # 基于长度和关键词的简单评分
        base_score = min(len(str(item)) / 100, 1.0)  # 长度评分

        # 类别特定的关键词加权
        category_keywords = {
            "world_premises": ["世界", "设定", "背景", "环境"],
            "dramatic_cores": ["冲突", "矛盾", "对立", "张力"],
            "character_archetypes": ["角色", "人物", "性格", "身份"],
            "symbolic_resonances": ["象征", "意义", "隐喻", "深层"],
            "narrative_hooks": ["情境", "开端", "起点", "引子"]
        }

        keywords = category_keywords.get(category, [])
        keyword_score = sum(1 for keyword in keywords if keyword in str(item)) / len(keywords)

        return min(base_score + keyword_score * 0.3, 1.0)

    def _process_aggregated_data(self, aggregated: Dict) -> Dict:
        """处理聚合数据：去重、排序、质量过滤"""
        processed = {}

        for category, items in aggregated.items():
            # 去重（基于内容相似性的简单去重）
            unique_items = []
            seen_contents = set()

            for item in items:
                content_key = str(item["content"]).lower().strip()
                if content_key not in seen_contents and len(content_key) > 10:  # 质量过滤
                    seen_contents.add(content_key)
                    unique_items.append(item)

            # 按权重排序
            unique_items.sort(key=lambda x: x["weight"], reverse=True)

            # 限制数量（保留前N个高质量项目）
            max_items = 8  # 默认值
            processed[category] = unique_items[:max_items]

        return processed

    def aggregate_and_save_inspirations(self, source_names: List[str], output_name: str = "aggregated") -> Dict:
        """
        聚合多个源的灵感数据并保存

        Args:
            source_names: 源文件名列表
            output_name: 输出文件名

        Returns:
            聚合后的灵感数据
        """
        analyses_list = []

        # 读取各个源的灵感数据
        for source_name in source_names:
            metadata_file = self.output_dir / f"{source_name}_inspirations_metadata.json"
            if metadata_file.exists():
                try:
                    with open(metadata_file, 'r', encoding='utf-8') as f:
                        segments_data = json.load(f)

                    # 提取每个段落的灵感数据
                    for segment in segments_data:
                        if isinstance(segment, dict):
                            # 提取灵感相关字段
                            inspiration_data = {}
                            for key in ["world_premises", "dramatic_cores", "character_archetypes",
                                      "symbolic_resonances", "narrative_hooks"]:
                                if key in segment:
                                    inspiration_data[key] = segment[key]

                            if inspiration_data:
                                analyses_list.append(inspiration_data)

                except Exception as e:
                    self.logger.warning(f"读取源文件失败 {source_name}: {e}")

        if not analyses_list:
            self.logger.warning("没有找到有效的灵感数据")
            return {}

        # 聚合数据
        aggregated = self.aggregate_inspirations(analyses_list)

        # 保存聚合结果
        output_file = self.output_dir / f"{output_name}_aggregated_inspirations.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(aggregated, f, ensure_ascii=False, indent=2)

        self.logger.info(f"聚合灵感数据完成，保存到: {output_file}")
        return aggregated