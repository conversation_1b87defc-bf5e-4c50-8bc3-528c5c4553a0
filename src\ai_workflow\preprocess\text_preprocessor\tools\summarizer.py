"""
故事灵感勘探器类
封装调用大语言模型进行故事灵感勘探的功能
"""

import logging
import json
from pathlib import Path
from typing import Dict, Any, Optional, List
import time

# 导入项目内的统一GPT客户端
from src.ai_workflow.utils.gpt_client import get_gpt_client
# 导入故事灵感勘探提示词
from .prompt.summary_prompt import SUMMARY_GENERATION_PROMPTS, STORY_INSPIRATION_EXTRACTOR_PROMPT


class Summarizer:
    """
    故事灵感勘探器类，使用项目统一的GPT客户端进行故事灵感勘探和摘要生成
    """

    def __init__(self, output_dir: str = "summaries"):
        """
        初始化故事灵感勘探器

        Args:
            output_dir: 输出文件保存目录
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)

        # 设置日志
        self.logger = logging.getLogger(__name__)

        # 获取统一的GPT客户端
        self.gpt_client = get_gpt_client()

        # 加载提示词
        self.prompts = SUMMARY_GENERATION_PROMPTS

        self.logger.info("故事灵感勘探器初始化完成，使用统一GPT客户端")

    def extract_story_inspiration(self, text: str) -> Dict[str, Any]:
        """
        从文本中勘探故事灵感

        Args:
            text: 要分析的文本

        Returns:
            包含故事灵感的字典，格式为：
            {
                "world_premises": [],
                "dramatic_cores": [],
                "character_archetypes": [],
                "symbolic_resonances": [],
                "narrative_hooks": [],
                "metadata": {...}
            }
        """
        if not text.strip():
            raise ValueError("输入文本不能为空")

        try:
            # 使用故事灵感勘探提示词
            system_prompt = STORY_INSPIRATION_EXTRACTOR_PROMPT["system"]
            user_prompt = STORY_INSPIRATION_EXTRACTOR_PROMPT["user"].format(text=text)

            # 调用统一的GPT客户端
            response = self.gpt_client.chat(
                system=system_prompt,
                prompt=user_prompt
            )

            # 解析JSON响应
            inspiration_data = self._parse_inspiration_response(response)

            # 添加元数据
            inspiration_data["metadata"] = {
                'original_length': len(text),
                'response_length': len(response),
                'timestamp': time.time(),
                'extraction_type': 'story_inspiration'
            }

            self.logger.info(f"故事灵感勘探完成，原文{len(text)}字符")
            return inspiration_data

        except Exception as e:
            self.logger.error(f"故事灵感勘探失败: {str(e)}")
            raise

    def _parse_inspiration_response(self, response: str) -> Dict[str, Any]:
        """解析故事灵感勘探的JSON响应"""
        try:
            # 尝试直接解析JSON
            inspiration = json.loads(response.strip())

            # 验证必要的字段
            required_fields = ["world_premises", "dramatic_cores", "character_archetypes",
                             "symbolic_resonances", "narrative_hooks"]

            for field in required_fields:
                if field not in inspiration:
                    inspiration[field] = []
                elif not isinstance(inspiration[field], list):
                    inspiration[field] = []

            return inspiration

        except json.JSONDecodeError as e:
            self.logger.warning(f"JSON解析失败，使用文本解析: {e}")
            # 后备文本解析方案
            return self._parse_inspiration_text_fallback(response)

    def _parse_inspiration_text_fallback(self, response: str) -> Dict[str, Any]:
        """故事灵感勘探的文本解析后备方案"""
        inspiration = {
            "world_premises": [],
            "dramatic_cores": [],
            "character_archetypes": [],
            "symbolic_resonances": [],
            "narrative_hooks": []
        }

        lines = response.split('\n')
        current_category = None

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 检测类别标题
            if "world_premises" in line.lower() or "世界前提" in line:
                current_category = "world_premises"
            elif "dramatic_cores" in line.lower() or "戏剧核心" in line:
                current_category = "dramatic_cores"
            elif "character_archetypes" in line.lower() or "角色原型" in line:
                current_category = "character_archetypes"
            elif "symbolic_resonances" in line.lower() or "象征共鸣" in line:
                current_category = "symbolic_resonances"
            elif "narrative_hooks" in line.lower() or "叙事钩子" in line:
                current_category = "narrative_hooks"
            elif current_category and (line.startswith('-') or line.startswith('•')):
                # 提取列表项
                item = line.lstrip('-•').strip()
                if item:
                    inspiration[current_category].append(item)

        return inspiration

    def extract_inspirations_from_segments(self, segments: List[Dict[str, Any]],
                                         source_name: str) -> List[Dict[str, Any]]:
        """
        批量从章节中勘探故事灵感

        Args:
            segments: 章节列表，每个元素应包含 'text' 字段
            source_name: 源文件名

        Returns:
            包含故事灵感的章节列表
        """
        inspiration_segments = []

        for i, segment in enumerate(segments):
            try:
                # 检查段落是否包含文本
                if 'text' not in segment or not segment['text'].strip():
                    self.logger.warning(f"章节{i+1}缺少文本内容，跳过灵感勘探")
                    inspiration_segments.append(segment)
                    continue

                # 勘探故事灵感
                inspiration_result = self.extract_story_inspiration(segment['text'])

                # 更新段落信息
                segment_with_inspiration = segment.copy()
                segment_with_inspiration['story_inspiration'] = inspiration_result

                # 保存灵感到文件
                inspiration_file = self.output_dir / f"{source_name}_seg{i+1}_inspiration.json"
                with open(inspiration_file, 'w', encoding='utf-8') as f:
                    json.dump(inspiration_result, f, ensure_ascii=False, indent=2)

                segment_with_inspiration['inspiration_file'] = str(inspiration_file)
                inspiration_segments.append(segment_with_inspiration)

                self.logger.info(f"章节{i+1}故事灵感勘探完成")

                # 添加延迟避免API限制
                time.sleep(1)

            except Exception as e:
                self.logger.error(f"章节{i+1}故事灵感勘探失败: {str(e)}")
                # 即使失败也保留原始段落
                inspiration_segments.append(segment)

        # 保存批量灵感元数据
        metadata_file = self.output_dir / f"{source_name}_inspirations_metadata.json"
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(inspiration_segments, f, ensure_ascii=False, indent=2)

        self.logger.info(f"批量故事灵感勘探完成: {source_name}, 共{len(inspiration_segments)}个章节")
        return inspiration_segments

    def summarize(self, text: str, prompt_type: str = "default_summary",
                 custom_prompt: Optional[str] = None) -> Dict[str, Any]:
        """
        生成文本摘要

        Args:
            text: 要摘要的文本
            prompt_type: 提示词类型，可选值：
                - "default_summary": 默认摘要
                - "segment_summary": 段落摘要
                - "cultural_heritage_summary": 文化遗产摘要
                - "structured_summary": 结构化摘要
            custom_prompt: 自定义提示词（优先级最高）

        Returns:
            包含摘要和元数据的字典
        """
        if not text.strip():
            raise ValueError("输入文本不能为空")

        try:
            # 选择提示词
            if custom_prompt:
                # 使用自定义提示词
                system_prompt = "你是一个专业的文本摘要专家。"
                user_prompt = custom_prompt.format(text=text)
            else:
                # 使用预定义提示词
                if prompt_type not in self.prompts:
                    self.logger.warning(f"未找到提示词类型 {prompt_type}，使用默认提示词")
                    prompt_type = "default_summary"

                prompt_config = self.prompts[prompt_type]
                system_prompt = prompt_config["system"]
                user_prompt = prompt_config["user"].format(text=text)

            # 调用统一的GPT客户端
            summary = self.gpt_client.chat(
                system=system_prompt,
                prompt=user_prompt
            )

            result = {
                'summary': summary,
                'original_length': len(text),
                'summary_length': len(summary),
                'compression_ratio': len(summary) / len(text) if len(text) > 0 else 0,
                'prompt_type': prompt_type,
                'timestamp': time.time()
            }

            self.logger.info(f"摘要生成完成，原文{len(text)}字符，摘要{len(summary)}字符")
            return result

        except Exception as e:
            self.logger.error(f"摘要生成失败: {str(e)}")
            raise

    def summarize_segments(self, segments: List[Dict[str, Any]],
                          source_name: str, prompt_type: str = "segment_summary") -> List[Dict[str, Any]]:
        """
        批量生成章节摘要

        Args:
            segments: 章节列表，每个元素应包含 'text' 字段
            source_name: 源文件名
            prompt_type: 摘要提示词类型

        Returns:
            包含摘要的章节列表
        """
        summarized_segments = []

        for i, segment in enumerate(segments):
            try:
                # 检查段落是否包含文本
                if 'text' not in segment or not segment['text'].strip():
                    self.logger.warning(f"章节{i+1}缺少文本内容，跳过摘要生成")
                    summarized_segments.append(segment)
                    continue

                # 生成摘要
                summary_result = self.summarize(segment['text'], prompt_type=prompt_type)

                # 更新段落信息
                segment_with_summary = segment.copy()
                segment_with_summary.update(summary_result)

                # 保存摘要到文件
                summary_file = self.output_dir / f"{source_name}_seg{i+1}_summary.txt"
                with open(summary_file, 'w', encoding='utf-8') as f:
                    f.write(summary_result['summary'])

                segment_with_summary['summary_file'] = str(summary_file)
                summarized_segments.append(segment_with_summary)

                self.logger.info(f"章节{i+1}摘要生成完成")

                # 添加延迟避免API限制
                time.sleep(1)

            except Exception as e:
                self.logger.error(f"章节{i+1}摘要生成失败: {str(e)}")
                # 即使失败也保留原始段落
                summarized_segments.append(segment)

        # 保存批量摘要元数据
        metadata_file = self.output_dir / f"{source_name}_summaries_metadata.json"
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(summarized_segments, f, ensure_ascii=False, indent=2)

        self.logger.info(f"批量摘要生成完成: {source_name}, 共{len(summarized_segments)}个章节")
        return summarized_segments