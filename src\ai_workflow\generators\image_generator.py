"""
简洁的图像生成器
使用RAG增强的图像生成功能，整合提示词工程
"""

import os
from typing import Dict, List, Optional, Union
import sys

# 添加路径以导入其他模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

# 导入统一配置管理
from src.config import get_config, get_section
from src.config.paths import OUTPUT_IMAGE_DIR

# 导入RAG增强图像生成器
from src.ai_workflow.rag.image.enhanced_image_generator import EnhancedImageGenerator

class ImageGenerator:
    """简洁的图像生成器类，使用RAG增强生成"""

    def __init__(self):
        """初始化图像生成器"""
        # 获取图像生成配置
        self.image_config = get_section('generators', 'image_generation')

        # 图像输出目录
        self.images_directory = OUTPUT_IMAGE_DIR
        os.makedirs(self.images_directory, exist_ok=True)

        # 初始化RAG增强图像生成器
        try:
            self.enhanced_generator = EnhancedImageGenerator(
                similarity_threshold=self.image_config.get('similarity_threshold', 0.2),
                ip_adapter_scale=self.image_config.get('ip_adapter_scale', 0.5),
                auto_build_index=self.image_config.get('auto_build_index', True)
            )
            print("RAG增强图像生成器初始化完成")
        except Exception as e:
            print(f"RAG增强图像生成器初始化失败: {e}")
            self.enhanced_generator = None
    
    def generate_image(self, prompt: str, image_name: str, mode: str = "background") -> str:
        """
        生成单张图像

        Args:
            prompt: 图像生成提示词
            image_name: 输出图像文件名（不含扩展名）
            mode: 生成模式，"background"为背景图，"character"为角色图

        Returns:
            生成结果状态："ok"表示成功，"error"表示失败
        """
        if not self.enhanced_generator:
            print("RAG增强图像生成器未初始化")
            return "error"

        try:
            # 根据模式调整提示词和参数
            enhanced_prompt = self._enhance_prompt(prompt, mode)
            width, height = self._get_dimensions(mode)

            print(f"生成图像: {image_name} (模式: {mode})")

            # 使用RAG增强生成器
            image, info = self.enhanced_generator.generate_enhanced_image(
                text_prompt=enhanced_prompt,
                width=width,
                height=height,
                num_inference_steps=self.image_config.get('default_steps', 25),
                guidance_scale=self.image_config.get('default_guidance_scale', 7.5),
                use_image_enhancement=True,
                save_image=False,  # 我们自己保存
                filename_prefix=None
            )

            # 保存到指定位置
            output_path = os.path.join(self.images_directory, f"{image_name}.png")
            image.save(output_path)
            print(f"图像已保存: {output_path}")

            return "ok"

        except Exception as e:
            print(f"图像生成失败: {e}")
            return "error"

    def _enhance_prompt(self, prompt: str, mode: str) -> str:
        """
        根据模式增强提示词

        Args:
            prompt: 原始提示词
            mode: 生成模式

        Returns:
            增强后的提示词
        """
        prompt_templates = self.image_config.get('prompt_templates', {})

        if mode == "background":
            template = prompt_templates.get('background', 'masterpiece, wallpaper, 8k, detailed CG, {prompt}, (no_human)')
        else:  # character
            template = prompt_templates.get('character', 'masterpiece, wallpaper, 8k, detailed CG, {prompt}, (upper_body), solo')

        return template.format(prompt=prompt)

    def _get_dimensions(self, mode: str) -> tuple:
        """
        根据模式获取图像尺寸

        Args:
            mode: 生成模式

        Returns:
            (width, height) 元组
        """
        dimensions = self.image_config.get('dimensions', {})

        if mode == "background":
            bg_dims = dimensions.get('background', {'width': 960, 'height': 540})
            return (bg_dims['width'], bg_dims['height'])
        else:  # character
            char_dims = dimensions.get('character', {'width': 512, 'height': 768})
            return (char_dims['width'], char_dims['height'])
    
    def generate_images_for_node(self, narrative_data: Dict) -> Dict:
        """
        为单个节点生成所需的所有图像
        
        Args:
            narrative_data: 节点叙事数据，包含场景描述、角色信息等
            
        Returns:
            包含生成图像信息的字典
        """
        node_id = narrative_data.get("node_id", "unknown")
        narrative_content = narrative_data.get("narrative_content", {})
        
        result = {
            "node_id": node_id,
            "backgrounds": [],
            "characters": [],
            "cg_images": [],
            "generation_status": "success"
        }
        
        try:
            # 生成背景图像
            settings = narrative_content.get("settings", [])
            for i, setting in enumerate(settings):
                bg_name = f"bg_{node_id}_{i+1}"
                status = self.generate_image(setting, bg_name, "background")
                
                result["backgrounds"].append({
                    "name": bg_name,
                    "description": setting,
                    "file_path": f"images/{bg_name}.png",
                    "status": status
                })
            
            # 生成角色立绘
            characters = narrative_content.get("characters", [])
            for char_info in characters:
                char_name = char_info.get("name", "unknown")
                char_description = char_info.get("description", "")
                
                if char_description:
                    char_image_name = f"char_{char_name}_{node_id}"
                    status = self.generate_image(char_description, char_image_name, "character")
                    
                    result["characters"].append({
                        "character_name": char_name,
                        "image_name": char_image_name,
                        "description": char_description,
                        "file_path": f"images/{char_image_name}.png",
                        "status": status
                    })
            
            # 生成特殊CG图像（如果有标记）
            cg_descriptions = narrative_content.get("cg_scenes", [])
            for i, cg_desc in enumerate(cg_descriptions):
                cg_name = f"cg_{node_id}_{i+1}"
                status = self.generate_image(cg_desc, cg_name, "background")
                
                result["cg_images"].append({
                    "name": cg_name,
                    "description": cg_desc,
                    "file_path": f"images/{cg_name}.png",
                    "status": status
                })
                
        except Exception as e:
            print(f"节点 {node_id} 图像生成失败: {e}")
            result["generation_status"] = "error"
            result["error_message"] = str(e)
        
        return result
    
    def generate_character_portraits(self, characters: List[Dict]) -> List[Dict]:
        """
        批量生成角色立绘
        
        Args:
            characters: 角色信息列表
            
        Returns:
            生成结果列表
        """
        results = []
        
        for char_info in characters:
            char_name = char_info.get("name", "unknown")
            char_description = char_info.get("appearance_description", "")
            
            if char_description:
                image_name = f"portrait_{char_name}"
                status = self.generate_image(char_description, image_name, "character")
                
                results.append({
                    "character_name": char_name,
                    "image_name": image_name,
                    "description": char_description,
                    "file_path": f"images/{image_name}.png",
                    "status": status
                })
        
        return results
    
    def get_generation_status(self) -> Dict:
        """
        获取生成器状态信息

        Returns:
            包含状态信息的字典
        """
        return {
            "mode": "rag_enhanced",
            "output_directory": self.images_directory,
            "available": self._check_availability(),
            "rag_stats": self._get_rag_stats()
        }

    def _check_availability(self) -> bool:
        """
        检查生成器可用性

        Returns:
            True表示可用，False表示不可用
        """
        return self.enhanced_generator is not None

    def _get_rag_stats(self) -> Dict:
        """
        获取RAG统计信息

        Returns:
            RAG统计信息字典
        """
        if not self.enhanced_generator:
            return {"status": "未初始化"}

        try:
            return self.enhanced_generator.get_system_info()
        except Exception as e:
            return {"status": f"错误: {e}"}


# 为了保持向后兼容性，提供便捷函数
def generate_image_unified(prompt: str, image_name: str, mode: str = "background") -> str:
    """
    统一的图像生成函数（向后兼容）
    
    Args:
        prompt: 图像生成提示词
        image_name: 输出图像文件名
        mode: 生成模式
        
    Returns:
        生成结果状态
    """
    generator = ImageGenerator()
    return generator.generate_image(prompt, image_name, mode)


if __name__ == "__main__":
    # 测试代码
    generator = ImageGenerator()
    print(f"生成器状态: {generator.get_generation_status()}")

    # 测试生成背景图
    test_result = generator.generate_image(
        "ancient Chinese temple in the rain, stone dog statue",
        "test_background",
        "background"
    )
    print(f"背景图生成结果: {test_result}")

    # 测试生成角色图
    test_result2 = generator.generate_image(
        "beautiful anime girl, traditional Chinese dress, long black hair",
        "test_character",
        "character"
    )
    print(f"角色图生成结果: {test_result2}")
