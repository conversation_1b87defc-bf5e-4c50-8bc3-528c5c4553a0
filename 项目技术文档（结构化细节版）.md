## 项目技术文档（结构化/细节版）

### 目录

- 开发框架与技术栈
- 项目结构与模块职责
- 配置与路径规范
- 数据与“数据库”结构（Embedding/索引/产物目录）
- 检索增强生成（RAG）系统
- 生成器模块（框架/叙事/图像/语音/音乐）
- 集成与产物（Ren’Py、UI）
- 主流程控制器（Pipeline）
- 内部API接口说明（类与方法约定）
- 日志/错误处理与资源管理
- 部署与运行建议

## 开发框架与技术栈

### 语言与运行环境

- Python 3.10+（建议Python 3.12）
- 生成游戏可以使用Windows，Linux等主流操作系统（只要有Python）
- 依托于ren'py框架的优势，生成的游戏在Windows， Android , IOS均可用

### 主要依赖

- 向量/索引：numpy、faiss-cpu/faiss-gpu、scikit-learn（余弦相似度）
- 深度学习：torch、transformers（CLIP等）
- 图像处理：Pillow (PIL)
- 语音/音乐：对应TTS/BGM生成模型（按配置）
- LLM调用：openai 官方SDK（gpt_client）或兼容服务（base_url可配置）
- 配置管理：自定义 config + paths

### 设计理念

- 配置驱动：路径、模型、超参均来自配置项且有默认值
- RAG优先：不硬编码参考资料，检索增强支持上下文
- JSON格式约定：生成器（尤其是剧情/叙事）输出统一JSON，便于机器消费
- 资源按需加载与释放：避免多模型并发导致显存爆

## 项目结构与模块职责

- src/
  - config/
    - paths.py（路径常量）
    - config_generators.json（生成器配置）
    - **init**.py（读取配置段 get_config/get_section）
  - ai_workflow/
    - preprocess/
      - text_preprocessor/（文本预处理：解析/切分/摘要/分块）
      - image_preprocessor/（如有：图像清洗/规范化）
    - rag/
      - text/（Text RAG：embedding、索引、检索）
        - embedding_tool.py（文本embedding与全局存储）
        - retriever.py（加载索引/检索/查询封装）
        - text_rag.py（编排：批量处理、统一搜索接口）
      - image/（Image RAG：图像索引与文本到图像检索）
        - clip_image_indexer.py（CLIP图像/文本embedding与索引构建）
        - text_image_retriever.py（加载索引并执行检索）
        - image_rag.py（组合使用 Indexer/Retriever 进行增强生成）
    - generators/
      - prompts/
        - framework_prompts.py（框架生成提示词：统一JSON要求）
        - narrative_prompts.py（叙事生成提示词：建议同样JSON化）
      - game_framework_generator.py（剧情骨架与角色设定；JSON解析）
      - node_narrative_generator.py（节点叙事生成；JSON解析）
      - image_generator.py（背景/立绘/CG生成，RAG增强）
      - vocal_generator.py（对白TTS/角色音色映射）
      - music_generator.py（BGM/环境音生成）
    - integrators/
      - renpy_integrator.py（导出到Ren’Py项目结构）
    - ui/
      - ui_design_module.py（UI主题/素材整合）
    - utils/
      - gpt_client.py（OpenAI官方SDK封装）
    - main_pipeline_controller.py（端到端流水线控制器）

## 配置与路径规范

核心路径由 src/config/paths.py 定义并被全局引用。关键目录（命名以示例为准）：

- 原始/预处理
  - PREPROCESS_TEXT_DIR: 预处理后的文本结果（包含 *_text_result.json）
  - PREPROCESS_IMAGE_DIR: 预处理后的图像集合
- Embedding/索引（“数据库”）
  - EMBEDDING_DB_DIR/
    - embedding/text/all_segments(.npz/.json)、all_chunks(.npz/.json)、all_segments_summary(.npz/.json)
    - image/{faiss_index.index, image_paths.json, clip_embeddings.npy}
- 产物输出
  - FRAMEWORK_DIR（剧情框架JSON）
  - OUTPUT_IMAGE_DIR（生成图片）
  - OUTPUT_AUDIO_DIR（语音/音乐）
  - RAG_TEXT_DIR / RAG_IMAGE_DIR（RAG阶段产物与统计）
  - RENPY_PROJECT_DIR（可选：整合到Ren’Py工程）

生成器超参（如图像分辨率、步数、提示词模板等）在 config_generators.json 统一管理。

## 数据与“数据库”结构（Embedding/索引/产物目录）

### 文本Embedding存储（统一大文件，便于检索）

- all_segments.npz / all_segments_metadata.json
- all_segments_summary.npz / all_segments_summary_metadata.json
- all_chunks.npz / all_chunks_metadata.json
- 元数据记录 source_name、segment_id/chunk_id、text_length、global_index 等

优势：

- 单文件聚合，索引构建与检索高效
- 易于统计与跨文档检索

### 文本索引

- 基于FAISS（优先）或纯numpy余弦相似度
- retriever.save_index() / retriever.load_index() 无需指定文件名，按 data_type 自动映射（segments/chunks/segments_summary）

### 图像索引（CLIP）

- index_data/
  - faiss_index.index
  - image_paths.json
  - clip_embeddings.npy

构建来源：PREPROCESS_IMAGE_DIR；检索：Text→Image（CLIP文本向量在索引中搜索）

## 检索增强生成（RAG）系统

### Text RAG（text_rag.py）

- EmbeddingTool
  - embed(texts: List[str]) → np.ndarray
  - embed_segments/chunks：追加到全局 all_*.npz，并维护元数据
  - get_global_embeddings_stats()：统计合计数量与来源
- Retriever
  - data_type: 'segments' | 'chunks' | 'segments_summary'
  - save_index() / load_index()（无文件名参数，自动推断）
  - query(vector, top_k) 若索引未加载自动load
  - search_text(query_text, embedding_tool, top_k)
- TextRAG
  - process_text_data(text_result_json) → 生成segments/chunks向量并保存
  - process_preprocessed_files() → 批量处理 PREPROCESS_TEXT_DIR，最后统一 rebuild index
  - search(dtype, query, top_k) → 标准格式结果:
    - content: 文本内容
    - source: 来源文件名
    - similarity: 相似度分数

### Image RAG（image_rag.py）

- CLIPImageIndexer：负责所有embedding（图像/文本）与索引构建
- TextImageRetriever：只负责加载索引与检索（使用 Indexer 的模型）
- ImageRAG：组合逻辑（按需使用Retriever/Indexer）进行检索增强生成（如IP-Adapter参考图），或仅检索

## 生成器模块

### 1) 框架生成（GameFrameworkGenerator）

- 输入：主题（theme）与RAG上下文（自动收集）
- 模型提示（JSON固定格式，且无 references 字段）：
  - mainline_nodes: [ { node_id, description, key_events[], characters[] } ]
  - sideline_nodes: […], node_relationships: […]
- 解析：
  - 先尝试 json.loads() → 验证字段与默认值 → 返回
  - 若失败，使用文本回退解析（关键字启发）
- 角色设定（JSON固定格式）：
  - [{ name, age, gender, appearance, personality, background, motivation, role, relationships{}, is_protagonist }]
- 产物：保存至 FRAMEWORK_DIR

### 2) 叙事生成（NodeNarrativeGenerator）

- 建议同样采用 JSON 输出（已对齐 NARRATIVE_GENERATION_PROMPTS 后）
- 推荐输出结构（示例）：
  - node_id: "A1"
  - narrative_content: { summary, dialogues: [ {speaker, text} ], stage_direction, choices? }
  - assets_hint: { background_tags[], character_tags[], cg_requirements[] }
- 采用与框架生成器相同的解析策略（JSON优先，文本回退）
- 输出提供给后续图像/语音/音乐生成与Ren’Py整合

### 3) 图像生成（ImageGenerator）

- 配置驱动：prompt模板、尺寸（background/character/CG）、steps、guidance、种子等
- 检索增强：Text→Image 检索到参考图，可通过 IP-Adapter 融合提升一致性
- 输出：OUTPUT_IMAGE_DIR，结构可按 node/asset 类型组织

### 4) 语音生成（VocalGenerator）

- 配置驱动：角色→音色映射；默认音色
- 输入：对白清单（来自叙事）
- 输出：角色独立的音频文件列表（wav/mp3），并在元数据中关联台词/角色

### 5) 音乐生成（MusicGenerator）

- 配置驱动：风格提示词/模型名称/时长
- 输入：节点/场景描述（可使用 RAG 提供的语义标签）
- 输出：BGM/过场音乐到 OUTPUT_AUDIO_DIR

## 集成与产物（Ren’Py、UI）

### RenpyIntegrator

- 将剧情/叙事/素材（背景、立绘、CG、语音、音乐）整合为 Ren’Py 脚本
- 生成 script.rpy、config.rpy 与资源目录结构（images/voice/music）
- 可按节点或场景文件拆分，支持分段调试

### UI Design Module

- UI主题、按钮/对话框风格资源整合
- 与 Ren’Py 主题配置对齐

## 主流程控制器（Pipeline）

MainPipelineController 负责组装端到端流程，支持按需初始化与使用后清理（节省显存）：

- 步骤示意（可配置启停）：
  1. 预处理（Text/Image）
  2. RAG索引构建（Text/segments|chunks|summary；Image）
  3. 框架生成（JSON）
  4. 叙事生成（JSON）
  5. 图像生成（含RAG参考）
  6. 语音/音乐生成
  7. Ren’Py整合与构建
- 模块实例通过 @property 延迟创建，用完 cleanup_module 精确释放（含 torch.cuda.empty_cache()）

## 内部API接口说明（类与方法约定）

以下为关键类的常用方法约定（示例）：

### 配置

- get_config(section, key) → 任意配置
- get_section(section, sub) → dict

### GPT客户端（OpenAI）

- GPTClient.chat(system, prompt, history=None, model=None, temperature=None, max_tokens=None) → str
- gpt(system, prompt, mode="common", history=None) → str（兼容函数）

### Text RAG

- EmbeddingTool
  - embed(texts: List[str]) → np.ndarray
  - embed_segments(segments, source_name, use_summary=False) → 追加到 all_segments(.npz)
  - embed_chunks(chunks, source_name) → 追加到 all_chunks(.npz)
  - get_global_embeddings_stats() → { segments: {count, files}, chunks: {…}, segments_summary: {…} }
- Retriever(data_type='segments'|'chunks'|'segments_summary')
  - save_index() / load_index() → 自动文件名映射（all_{data_type}）
  - search_text(query_text, embedding_tool, top_k=5) → List[ {rank, similarity, metadata{...}} ]
  - query(query_vector, top_k=5) → 若未加载自动load
  - get_stats() → { vector_count, index_loaded, index_dir, data_type }
- TextRAG
  - process_text_data(text_result_json, build_index=True) → 生成embedding并延后索引构建
  - process_preprocessed_files(input_dir=None) → 批处理并统一重建索引
  - rebuild_retriever_index() → 从全局文件构建检索索引
  - search(dtype, query, top_k=5) → 统一返回:
    - { content, source, similarity }

### Image RAG

- CLIPImageIndexer(model_path=None)
  - extract_image_embedding(image_path) / extract_text_embedding(text)
  - build_index_from_directory(image_dir) → 构建faiss索引
  - save_index()/load_index()（内部管理 path）
- TextImageRetriever(similarity_threshold=0.2)
  - load_index() → 加载 index + image_paths.json
  - search_similar_images(text, top_k=5) → [(image_path, similarity)]
  - rebuild_index(image_dir=None) → 默认 PREPROCESS_IMAGE_DIR
- ImageRAG(similarity_threshold=0.2, ip_adapter_scale=0.5)
  - build_index(image_dir=None)
  - search_similar_images(text, top_k=5)
  - generate(prompt, negative_prompt=None, width=..., height=..., steps=..., guidance_scale=..., seed=None, use_retrieval=True, save=True, filename_prefix=None) → (PIL.Image, info)

### 框架/叙事生成

- GameFrameworkGenerator
  - generate_story_framework(theme, keywords=None) → { theme, story_skeleton(JSON), characters(JSON), reference_context, generation_metadata }
  - parse_story_skeleton(response_str) → JSON（无 references 字段）
  - parse_character_settings(response_str) → JSON
- NodeNarrativeGenerator（建议/对齐后）
  - generate_for_nodes(story_skeleton, reference_context) → List[{ node_id, narrative_content(JSON), assets_hint(JSON) }]
  - parse_narrative_json(response_str) → JSON，统一输出格式

## 日志/错误处理与资源管理

- 日志：logging提供进度/统计；异常时打印关键信息
- JSON解析：优先json.loads，失败则fallback文本解析（确保鲁棒性）
- 资源释放：GPU模型/索引在不再使用时立刻释放，清空CUDA缓存
- RAG索引：缺失时自动加载/自动构建（避免手动干预）

## 部署与运行建议

- 准备
  - 安装依赖（openai、torch、transformers、faiss、numpy、Pillow 等）
  - 配置 src/config/config_generators.json 与 src/config/paths.py
  - 设置OpenAI密钥与base_url（如使用兼容服务）
- 数据
  - 将预处理结果放入 PREPROCESS_TEXT_DIR（*_text_result.json）
  - 将图像素材放入 PREPROCESS_IMAGE_DIR
- 运行
  - 分步执行（预处理→RAG→生成→整合），或使用 MainPipelineController 端到端
- 硬件
  - 建议GPU（图像/语音/音乐更高效）
  - 显存有限时：按需初始化+模块间清理，分批运行

以上文档覆盖了整体架构与模块接口，着重在“开发框架/数据结构/API说明”。若需要，我可以补充：

- narrative_prompts.py 的JSON规范草案
- Ren’Py 导出结构（rpy模板约定）
- 具体例子（从主题到最终可运行Ren’Py项目的端到端样例）