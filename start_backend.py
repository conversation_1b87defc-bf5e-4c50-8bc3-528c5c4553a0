#!/usr/bin/env python3
"""
AI-GAL 后端服务启动脚本
"""

import os
import sys
import subprocess
import argparse

def check_dependencies():
    """检查依赖是否安装"""
    required_packages = [
        "fastapi",
        "uvicorn",
        "pydantic"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r backend/requirements.txt")
        return False
    
    return True

def check_config():
    """检查配置文件"""
    config_path = "config.ini"
    if not os.path.exists(config_path):
        print("⚠️  配置文件 config.ini 不存在")
        print("请确保配置文件存在并正确设置API密钥")
        return False
    
    print("✅ 配置文件已找到")
    return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="AI-GAL 后端服务启动器")
    parser.add_argument("--host", default="127.0.0.1", help="服务器地址")
    parser.add_argument("--port", type=int, default=8080, help="服务器端口")
    parser.add_argument("--reload", action="store_true", help="开启自动重载")
    parser.add_argument("--test", action="store_true", help="启动后运行测试")
    
    args = parser.parse_args()
    
    print("🚀 AI-GAL 后端服务启动器")
    print("=" * 50)
    
    # 检查依赖
    print("🔍 检查依赖...")
    if not check_dependencies():
        sys.exit(1)
    
    # 检查配置
    print("🔍 检查配置...")
    if not check_config():
        print("⚠️  配置检查失败，但服务仍可启动")
    
    print("✅ 环境检查完成")
    print()
    
    # 启动服务
    print(f"🌐 启动后端服务...")
    print(f"   地址: http://{args.host}:{args.port}")
    print(f"   API文档: http://{args.host}:{args.port}/docs")
    print(f"   管理界面: http://{args.host}:{args.port}/redoc")
    print()
    
    try:
        # 构建启动命令
        cmd = [
            sys.executable, "-m", "backend.server",
            "--host", args.host,
            "--port", str(args.port)
        ]
        
        if args.reload:
            cmd.append("--reload")
        
        # 启动服务
        process = subprocess.Popen(cmd)
        
        # 如果需要测试，等待服务启动后运行测试
        if args.test:
            import time
            print("⏳ 等待服务启动...")
            time.sleep(3)
            
            print("🧪 运行服务测试...")
            test_cmd = [
                sys.executable, "backend/test_backend.py",
                "--url", f"http://{args.host}:{args.port}"
            ]
            subprocess.run(test_cmd)
        
        # 等待服务进程
        process.wait()
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断，服务停止")
    except Exception as e:
        print(f"\n❌ 服务启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
