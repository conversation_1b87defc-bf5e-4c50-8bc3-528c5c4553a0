{"openai": {"api_key": "sk-bed7b2327ff446b1aac652e0ddf1e5b4", "base_url": "https://api.deepseek.com/v1", "model": "deepseek-reasoner", "temperature": 0.7, "max_tokens": 4000, "timeout": 120, "retry_times": 3, "retry_delay": 1}, "dashscope": {"api_key": "", "base_url": "https://dashscope.aliyuncs.com/api/v1", "model": "qwen-turbo", "temperature": 0.7, "max_tokens": 4000, "timeout": 120, "retry_times": 3, "retry_delay": 1}, "custom": {"api_key": "", "base_url": "", "model": "", "temperature": 0.7, "max_tokens": 4000, "timeout": 120, "retry_times": 3, "retry_delay": 1}, "default_provider": "openai", "fallback_providers": ["dashscope", "custom"], "enable_fallback": true, "rate_limiting": {"requests_per_minute": 60, "tokens_per_minute": 100000, "enable_rate_limit": false}, "caching": {"enable_cache": true, "cache_ttl": 3600, "max_cache_size": 1000}}