# 2025-07-12 V0.1 初始项目
🎉 **完全重构原项目！基于大模型技术实现的文化遗产严肃游戏生成系统**

## 🚀 核心功能重构
1. **素材预处理模块** - 支持PDF文档、图像语义分析、spaCy文本分段
2. **RAG检索系统** - 智能检索相关文化内容，构建双模态知识库
3. **结构化节点生成** - 明确的剧情节点编号系统(A1, A2, B1等)
4. **增强图像生成** - 结合文化背景的智能Stable Diffusion提示词生成
5. **完整音频系统** - MusicGen背景音乐 + GPT-SoVITS语音合成集成
6. **高级UI设计** - 文化特色界面、无障碍功能、知识收集系统
7. **完整流程控制** - 从素材输入到游戏输出的一键生成
8. **系统测试工具** - 自动化测试和性能优化

## 🎯 技术亮点
- **多阶段处理**: Deepseek-R1 Phase I/II/III分阶段生成
- **标记化输出**: [Setting:]、[Music:]等标记支持后续处理
- **文化遗产专用**: 专门针对文化遗产主题优化的严肃游戏系统
- **模块化设计**: 10个独立模块，易于维护和扩展

## 🎮 使用方式
```bash
# 主启动器（推荐）
python launcher.py

# 直接运行测试
python test_and_optimization.py
```


# 🛠️ 环境要求

## 必需配置
- **GPT API密钥** (Deepseek-R1推荐)
- **Python 3.8+** 环境

## 推荐依赖
```bash
pip install spacy requests pillow numpy scikit-learn
python -m spacy download zh_core_web_sm  # 中文文本处理
```

## 可选配置（增强功能）
- **Stable Diffusion API** - 图像生成
- **GPT-SoVITS** - 语音合成
- **MusicGen API** - 背景音乐生成
- **CLIP-Interrogator** - 图像语义分析

## 快速开始
1. 克隆项目：`git clone https://github.com/tamikip/遗游记.git`
2. 安装依赖：`pip install -r config/requirements.txt`
3. 安装spaCy中文模型：`python -m spacy download zh_core_web_sm`
4. 配置API密钥：编辑 `config/config.ini`
5. 启动系统：`python launcher.py`

- 📖 项目文档：
- 💬 QQ群：
# 🎮 遗游记 Enhanced 能做什么？

## 🏛️ 文化遗产严肃游戏生成
专门为文化遗产主题设计的完整游戏生成系统，从原始素材到可运行游戏的端到端解决方案。

## 📚 核心功能模块

### 1. 智能素材处理
- **文档解析**：支持PDF、TXT等多种格式
- **文本分段**：使用spaCy进行智能分段
- **摘要生成**：生成结构化汇总文档(SAD)
- **图像分析**：构建视觉语义库存(VSI)

### 2. RAG增强生成
- **智能检索**：基于向量数据库的语义检索
- **上下文增强**：为生成提供相关文化背景
- **双模态支持**：同时处理文本和图像信息

### 3. 结构化剧情生成
- **框架构建**：生成主线剧情骨架(A1, A2, B1...)
- **角色设定**：详细的角色背景和关系网络
- **节点叙事**：每个节点的详细对话和场景描述

### 4. 多媒体资源生成
- **智能图像**：结合文化背景的Stable Diffusion生成
- **背景音乐**：MusicGen情感化音乐生成
- **角色配音**：GPT-SoVITS高质量语音合成

### 5. 完整游戏整合
- **Ren'Py脚本**：自动生成完整的.rpy文件
- **UI设计**：文化特色界面和交互逻辑
- **分支系统**：智能的选择和跳转机制

## 🚀 快速上手指南

### 第一步：环境配置
1. **安装Python依赖**：
   ```bash
   pip install -r config/requirements.txt
   python -m spacy download zh_core_web_sm
   ```

2. **配置API密钥**：
   编辑 `config/config.ini` 文件，填入您的API密钥：
   ```ini
   [CHATGPT]
   gpt_key = your_deepseek_api_key
   base_url = https://api.deepseek.com
   model = deepseek-reasoner
   ```

### 第二步：开始使用
```bash
python launcher.py
```
选择"运行系统测试"确保环境正常，然后选择"生成完整游戏"

### 第四步：享受成果
生成完成后，在 `generated_game` 目录找到您的Ren'Py游戏！

## 许可证

本项目基于 [Ren'Py](https://www.renpy.org/) 项目进行二次开发，并遵循以下许可证条款：

- 本项目的主要部分遵循 [MIT 许可证](LICENSE)。
- 本项目中包含的某些组件和库遵循其他许可证，包括但不限于：
  - [GNU 宽通用公共许可证 (LGPL)](https://www.gnu.org/licenses/lgpl-3.0.html)
  - [Zlib 许可证](https://opensource.org/licenses/Zlib)
  - 其他相关许可证请参见各自的许可证文件。

请确保在分发本项目时，包含所有相关的许可证文件。



