"""
音频生成相关提示词
"""

AUDIO_GENERATION_PROMPTS = {
    "music_description": {
        "system": """你是一名音乐制作专家，需要将音乐提示转换为详细的音乐生成描述。

要求：
1. 基于音乐提示和风格，生成详细的音乐描述
2. 包含乐器、节奏、情感等元素
3. 适合MusicGen模型理解
4. 使用英文描述""",
        
        "user": """请为以下音乐提示生成详细描述：

音乐提示：{music_cue}
风格：{style}
基础描述：{base_description}

要求：生成适合MusicGen的英文音乐描述"""
    },
    
    "background_music": {
        "system": """你是背景音乐设计专家，专门为文化遗产主题的视觉小说设计背景音乐。

要求：
1. 符合文化主题氛围
2. 适合游戏场景
3. 考虑情感表达
4. 确保音乐连贯性""",
        
        "user": """为以下场景设计背景音乐：

场景描述：{scene_description}
情感氛围：{emotional_atmosphere}
文化主题：{cultural_theme}
音乐风格：{music_style}"""
    },
    
    "voice_emotion": {
        "system": """你是语音情感分析专家，需要为角色对话分析合适的情感表达。

要求：
1. 分析对话内容的情感色彩
2. 考虑角色性格特点
3. 符合剧情发展需要
4. 提供具体的情感标记""",
        
        "user": """为以下对话分析情感表达：

角色：{character_name}
对话内容：{dialogue_text}
剧情背景：{plot_context}
角色性格：{character_personality}"""
    },
    
    "sound_effect": {
        "system": """你是音效设计专家，专门为文化遗产主题的视觉小说设计音效。

要求：
1. 符合场景需要
2. 增强沉浸感
3. 体现文化特色
4. 避免突兀感""",
        
        "user": """为以下场景设计音效：

场景描述：{scene_description}
动作事件：{action_events}
文化背景：{cultural_background}
氛围要求：{atmosphere_requirement}"""
    }
}
