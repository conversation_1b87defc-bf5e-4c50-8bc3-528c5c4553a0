[CHATGPT]
gpt_key = 
base_url = 
model = 

[SOVITS]
if_cloud = False
语音key = 
version = 0
gpt_model_path = 
sovits_model_path = 
sovits_url1 = 
sovits_url2 = 
sovits_url3 = 
sovits_url4 = 
sovits_url5 = 
sovits_url6 = 
model1 = 
model2 = 
model3 = 
model4 = 
model5 = 
model6 = 

[AI绘画]
if_cloud = False
draw_key = 
sd_url = 
sd_url2 = 
sd_url3 = 
sd_url4 = 
sd_url5 = 
sd_url6 = 
sd_url7 = 
sd_url8 = 
sd_url9 = 
sd_url10 = 
sd_url11 = 
sd_url12 = 
sd_url13 = 
sd_url14 = 
sd_url15 = 
sd_url16 = 
sd_url17 = 
sd_url18 = 
sd_url19 = 
sd_url20 = 

[AI音乐]
if_on = False
music_key = 

[素材预处理]
spacy_model = zh_core_web_sm
max_text_chunk_size = 1000
enable_pdf_processing = True

[RAG检索]
similarity_threshold = 0.7
max_results = 10
