"""
新图像生成器使用示例
展示如何使用RAG增强的图像生成功能
"""

from src.ai_workflow.generators.image_generator import ImageGenerator


def test_basic_generation():
    """测试基础图像生成"""
    print("=== 基础图像生成测试 ===")
    
    generator = ImageGenerator()
    
    # 检查状态
    status = generator.get_generation_status()
    print(f"生成器状态: {status}")
    
    if not status["available"]:
        print("生成器不可用，请检查配置")
        return
    
    # 生成背景图
    print("\n生成背景图...")
    result = generator.generate_image(
        prompt="古代中国庭院，石桥流水，樱花飞舞",
        image_name="test_bg_garden",
        mode="background"
    )
    print(f"背景图生成结果: {result}")
    
    # 生成角色图
    print("\n生成角色图...")
    result = generator.generate_image(
        prompt="美丽的古装少女，长发飘逸，温柔笑容",
        image_name="test_char_girl",
        mode="character"
    )
    print(f"角色图生成结果: {result}")


def test_batch_generation():
    """测试批量生成"""
    print("\n=== 批量生成测试 ===")
    
    generator = ImageGenerator()
    
    # 测试数据
    test_data = {
        "node_001": {
            "narrative_content": {
                "settings": [
                    "古代书房，书架林立，烛光摇曳",
                    "竹林小径，月光如水，虫鸣阵阵"
                ],
                "characters": [
                    {
                        "name": "李清照",
                        "description": "才女诗人，优雅气质，手持书卷"
                    },
                    {
                        "name": "苏轼",
                        "description": "文豪学者，儒雅风度，胸怀天下"
                    }
                ],
                "cg_scenes": [
                    "月下对诗，才子佳人，诗意盎然"
                ]
            }
        }
    }
    
    # 为节点生成图像
    for node_id, data in test_data.items():
        print(f"\n为节点 {node_id} 生成图像...")
        result = generator.generate_images_for_node({
            "node_id": node_id,
            "narrative_content": data["narrative_content"]
        })
        
        print(f"节点 {node_id} 生成结果:")
        print(f"  状态: {result['generation_status']}")
        print(f"  背景图: {len(result['backgrounds'])} 张")
        print(f"  角色图: {len(result['characters'])} 张")
        print(f"  CG图: {len(result['cg_images'])} 张")


def test_character_portraits():
    """测试角色立绘生成"""
    print("\n=== 角色立绘生成测试 ===")
    
    generator = ImageGenerator()
    
    # 角色数据
    characters = [
        {
            "name": "林黛玉",
            "appearance_description": "娇弱美人，柳眉杏眼，楚楚动人，古典美女"
        },
        {
            "name": "贾宝玉",
            "appearance_description": "翩翩公子，温文尔雅，俊美少年，贵族气质"
        },
        {
            "name": "王熙凤",
            "appearance_description": "精明能干，美艳动人，华贵服饰，威严气质"
        }
    ]
    
    # 批量生成角色立绘
    results = generator.generate_character_portraits(characters)
    
    print("角色立绘生成结果:")
    for result in results:
        print(f"  {result['character_name']}: {result['status']}")
        if result['status'] == 'ok':
            print(f"    文件: {result['file_path']}")


def test_rag_features():
    """测试RAG增强功能"""
    print("\n=== RAG增强功能测试 ===")
    
    generator = ImageGenerator()
    
    if not generator.enhanced_generator:
        print("RAG增强生成器未初始化")
        return
    
    # 获取RAG统计信息
    rag_stats = generator._get_rag_stats()
    print("RAG系统信息:")
    print(f"  检索系统: {rag_stats.get('retrieval', {})}")
    print(f"  生成系统: {rag_stats.get('generation', {})}")
    print(f"  设置: {rag_stats.get('settings', {})}")
    
    # 测试检索功能
    print("\n测试图像检索...")
    try:
        similar_images = generator.enhanced_generator.retriever.search_similar_images(
            "传统建筑", top_k=3
        )
        print("检索到的相似图像:")
        for i, (path, score) in enumerate(similar_images, 1):
            print(f"  {i}. {path} (相似度: {score:.3f})")
    except Exception as e:
        print(f"检索测试失败: {e}")


def main():
    """运行所有测试"""
    try:
        test_basic_generation()
        test_batch_generation()
        test_character_portraits()
        test_rag_features()
        
        print("\n=== 所有测试完成 ===")
        
    except Exception as e:
        print(f"测试运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
