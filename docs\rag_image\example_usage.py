"""
ImageRAG 使用示例
演示如何使用 ImageRAG 类进行图像检索增强生成
"""

import os
from image_rag import ImageRAG


def basic_usage_example():
    """基础使用示例"""
    print("=== ImageRAG 基础使用示例 ===")
    
    # 1. 初始化 ImageRAG
    rag = ImageRAG(
        similarity_threshold=0.2,  # 相似度阈值
        ip_adapter_scale=0.5,      # IP-Adapter影响权重
        auto_build_index=True      # 自动构建索引
    )
    
    # 2. 查看系统状态
    stats = rag.get_stats()
    print(f"索引图像数量: {stats['retrieval'].get('total_images', 0)}")
    print(f"当前设置 - 相似度阈值: {rag.similarity_threshold}, IP-Adapter权重: {rag.ip_adapter_scale}")
    
    # 3. 生成单张图像
    print("\n生成图像...")
    image, info = rag.generate(
        prompt="传统中国花纹，精美刺绣，高质量，详细",
        width=512,
        height=768,
        steps=25,
        use_retrieval=True,  # 启用检索增强
        save=True
    )
    
    print(f"生成完成:")
    print(f"  - 生成时间: {info['generation_time']:.2f}秒")
    print(f"  - 使用检索增强: {info['use_image_enhancement']}")
    if info['retrieved_image']:
        print(f"  - 参考图像: {os.path.basename(info['retrieved_image'])}")
        print(f"  - 相似度: {info['similarity_score']:.3f}")
    print(f"  - 保存路径: {info['output_path']}")


def search_example():
    """图像搜索示例"""
    print("\n=== 图像搜索示例 ===")
    
    rag = ImageRAG()
    
    # 搜索相似图像
    query_texts = [
        "传统花纹",
        "古代建筑",
        "中国风景"
    ]
    
    for query in query_texts:
        print(f"\n搜索: '{query}'")
        
        # 搜索前5个最相似的图像
        similar_images = rag.search_similar_images(query, top_k=5)
        
        if similar_images:
            for i, (path, score) in enumerate(similar_images, 1):
                print(f"  {i}. {os.path.basename(path)} (相似度: {score:.3f})")
        else:
            print("  未找到相似图像")
        
        # 查找最佳匹配
        best_match = rag.find_best_match(query)
        if best_match:
            path, score = best_match
            print(f"  最佳匹配: {os.path.basename(path)} (相似度: {score:.3f})")


def batch_generation_example():
    """批量生成示例"""
    print("\n=== 批量生成示例 ===")
    
    rag = ImageRAG(
        similarity_threshold=0.15,  # 降低阈值以获得更多匹配
        ip_adapter_scale=0.6
    )
    
    # 批量生成多张图像
    prompts = [
        "传统中国花纹，红色背景",
        "古代宫殿建筑，金色装饰",
        "山水画风景，水墨画风格"
    ]
    
    print(f"批量生成 {len(prompts)} 张图像...")
    results = rag.batch_generate(
        prompts=prompts,
        width=512,
        height=512,
        steps=20,
        use_retrieval=True
    )
    
    for i, (image, info) in enumerate(results, 1):
        if image is not None:
            print(f"图像 {i}: 生成成功")
            print(f"  - 提示词: {info['text_prompt'][:30]}...")
            print(f"  - 生成时间: {info['generation_time']:.2f}秒")
            if info['retrieved_image']:
                print(f"  - 参考图像: {os.path.basename(info['retrieved_image'])}")
        else:
            print(f"图像 {i}: 生成失败 - {info.get('error', '未知错误')}")


def settings_management_example():
    """设置管理示例"""
    print("\n=== 设置管理示例 ===")
    
    rag = ImageRAG()
    
    # 查看当前设置
    print(f"初始设置:")
    print(f"  - 相似度阈值: {rag.similarity_threshold}")
    print(f"  - IP-Adapter权重: {rag.ip_adapter_scale}")
    
    # 更新设置
    print("\n更新设置...")
    rag.update_settings(
        similarity_threshold=0.3,
        ip_adapter_scale=0.7
    )
    
    print(f"更新后设置:")
    print(f"  - 相似度阈值: {rag.similarity_threshold}")
    print(f"  - IP-Adapter权重: {rag.ip_adapter_scale}")
    
    # 也可以通过属性直接设置
    rag.similarity_threshold = 0.25
    rag.ip_adapter_scale = 0.4
    
    print(f"通过属性设置后:")
    print(f"  - 相似度阈值: {rag.similarity_threshold}")
    print(f"  - IP-Adapter权重: {rag.ip_adapter_scale}")


def index_management_example():
    """索引管理示例"""
    print("\n=== 索引管理示例 ===")
    
    rag = ImageRAG(auto_build_index=False)  # 不自动构建索引
    
    # 查看索引信息
    index_info = rag.get_index_info()
    print(f"索引状态: {index_info}")
    
    # 手动重建索引
    print("\n重建索引...")
    success = rag.build_index()
    
    if success:
        print("索引重建成功")
        index_info = rag.get_index_info()
        print(f"新索引信息: {index_info}")
    else:
        print("索引重建失败")


def comparison_example():
    """对比示例：有无检索增强的差异"""
    print("\n=== 对比示例：有无检索增强 ===")
    
    rag = ImageRAG()
    prompt = "传统中国花纹，精美刺绣"
    
    # 不使用检索增强
    print("1. 纯文本生成（无检索增强）:")
    image1, info1 = rag.generate(
        prompt=prompt,
        use_retrieval=False,
        filename_prefix="no_retrieval"
    )
    print(f"   生成时间: {info1['generation_time']:.2f}秒")
    print(f"   保存路径: {info1['output_path']}")
    
    # 使用检索增强
    print("\n2. 检索增强生成:")
    image2, info2 = rag.generate(
        prompt=prompt,
        use_retrieval=True,
        filename_prefix="with_retrieval"
    )
    print(f"   生成时间: {info2['generation_time']:.2f}秒")
    if info2['retrieved_image']:
        print(f"   参考图像: {os.path.basename(info2['retrieved_image'])}")
        print(f"   相似度: {info2['similarity_score']:.3f}")
    print(f"   保存路径: {info2['output_path']}")


def main():
    """运行所有示例"""
    try:
        basic_usage_example()
        search_example()
        batch_generation_example()
        settings_management_example()
        index_management_example()
        comparison_example()
        
        print("\n=== 所有示例运行完成 ===")
        
    except Exception as e:
        print(f"示例运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
