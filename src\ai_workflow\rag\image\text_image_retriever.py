"""
文本图像检索模块
负责加载索引和检索逻辑，使用CLIPImageIndexer进行embedding
"""

import os
import numpy as np
import faiss
import json
from typing import List, Tuple, Optional
import logging
from src.config.paths import CLIP_MODEL_DIR, PREPROCESS_IMAGE_DIR, RAG_IMAGE_INDEX_DIR
from .clip_image_indexer import CLIPImageIndexer

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TextImageRetriever:
    """文本图像检索器 - 负责加载索引和检索逻辑"""

    def __init__(self, model_path: Optional[str] = None, similarity_threshold: float = 0.2):
        """
        初始化文本图像检索器

        Args:
            model_path: CLIP模型路径，None时使用默认路径
            similarity_threshold: 相似度阈值，低于此值的检索结果将被过滤
        """
        self.similarity_threshold = similarity_threshold

        # 索引相关
        self.index = None
        self.indexer=None
        self.image_paths = []

        # 文件路径
        self.index_dir = str(RAG_IMAGE_INDEX_DIR)
        self.index_file = os.path.join(self.index_dir, "faiss_index.index")
        self.paths_file = os.path.join(self.index_dir, "image_paths.json")

        # 尝试加载已有索引
        if not self.load_index():
            logger.warning("No existing index found. Please build index first.")

    def load_index(self) -> bool:
        """
        加载FAISS索引和图像路径

        Returns:
            是否成功加载索引
        """
        try:
            if os.path.exists(self.index_file) and os.path.exists(self.paths_file):
                # 加载FAISS索引
                self.index = faiss.read_index(self.index_file)

                # 加载图像路径
                with open(self.paths_file, 'r', encoding='utf-8') as f:
                    self.image_paths = json.load(f)

                logger.info(f"索引加载成功，包含 {len(self.image_paths)} 张图像")
                return True
            else:
                logger.warning("索引文件不存在")
                return False
        except Exception as e:
            logger.error(f"加载索引失败: {e}")
            return False
    
    def extract_text_embedding(self, text: str) -> np.ndarray:
        """
        提取文本的CLIP嵌入（使用indexer的方法）

        Args:
            text: 输入文本

        Returns:
            文本嵌入向量
        """
        return self.indexer.extract_text_embedding(text)
    
    def search_similar_images(self, text: str, top_k: int = 5) -> List[Tuple[str, float]]:
        """
        根据文本检索相似图像
        
        Args:
            text: 查询文本
            top_k: 返回前k个最相似的图像
            
        Returns:
            (图像路径, 相似度分数)的列表
        """
        # 初始化索引器（负责embedding）
        self.indexer = CLIPImageIndexer()
        if self.index is None:
            logger.error("No index loaded. Please build or load index first.")
            return []

        # 提取文本嵌入
        text_embedding = self.extract_text_embedding(text)
        if text_embedding is None:
            return []

        # 在索引中搜索
        text_embedding = text_embedding.reshape(1, -1).astype('float32')
        distances, indices = self.index.search(text_embedding, top_k)

        # 处理结果
        results = []
        for distance, idx in zip(distances[0], indices[0]):
            if idx >= 0 and idx < len(self.image_paths):
                image_path = self.image_paths[idx]
                similarity = float(distance)  # 内积距离，值越大越相似

                # 应用相似度阈值过滤
                if similarity >= self.similarity_threshold:
                    results.append((image_path, similarity))
                    logger.info(f"Found similar image: {os.path.basename(image_path)} (similarity: {similarity:.3f})")
                else:
                    logger.info(f"Image {os.path.basename(image_path)} filtered out (similarity: {similarity:.3f} < {self.similarity_threshold})")

        return results
    
    def search_best_match(self, text: str) -> Optional[Tuple[str, float]]:
        """
        检索最佳匹配的图像
        
        Args:
            text: 查询文本
            
        Returns:
            最佳匹配的(图像路径, 相似度分数)，如果没有符合阈值的结果则返回None
        """
        results = self.search_similar_images(text, top_k=1)
        return results[0] if results else None
    
    def batch_search(self, texts: List[str], top_k: int = 5) -> List[List[Tuple[str, float]]]:
        """
        批量检索多个文本对应的相似图像
        
        Args:
            texts: 文本列表
            top_k: 每个文本返回前k个最相似的图像
            
        Returns:
            每个文本对应的检索结果列表
        """
        results = []
        for text in texts:
            text_results = self.search_similar_images(text, top_k)
            results.append(text_results)
        return results
    
    def update_similarity_threshold(self, threshold: float):
        """
        更新相似度阈值
        
        Args:
            threshold: 新的相似度阈值
        """
        self.similarity_threshold = threshold
        logger.info(f"Similarity threshold updated to {threshold}")
    
    def rebuild_index(self, image_dir: Optional[str] = None) -> bool:
        """
        重新构建索引

        Args:
            image_dir: 图像目录路径，None时使用默认预处理图像目录

        Returns:
            是否成功重建
        """
        if image_dir is None:
            image_dir = str(PREPROCESS_IMAGE_DIR)

        logger.info(f"Rebuilding index from directory: {image_dir}")
        if self.indexer.build_index_from_directory(image_dir):
            if self.indexer.save_index():
                # 重新加载索引到本地
                return self.load_index()
        return False

    def get_retrieval_stats(self) -> dict:
        """
        获取检索统计信息

        Returns:
            检索统计信息
        """
        return {
            "total_images": len(self.image_paths) if self.image_paths else 0,
            "index_loaded": self.index is not None,
            "similarity_threshold": self.similarity_threshold,
            "index_dir": self.index_dir
        }
