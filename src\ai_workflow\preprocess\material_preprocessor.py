"""
素材预处理模块
简化版本：对输入目录进行分类，调用对应的预处理器
"""

import os
from typing import List, Dict, Tuple, Any
import logging

# 导入图像预处理器
from .image_preprocess import ImagePreprocessor
from .text_preprocessor import TextProcessor

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MaterialPreprocessor:
    """简化的素材预处理器 - 分类并调用对应的预处理器"""

    def __init__(self):
        """初始化素材预处理器"""
        # 初始化子预处理器
        self.image_preprocessor = ImagePreprocessor()
        self.text_preprocessor = TextProcessor()

        # 支持的文件格式
        self.text_formats = {'.txt', '.md', '.pdf', '.docx', '.doc'}
        self.image_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.gif', '.webp'}

        logger.info("Material preprocessor initialized")

    def classify_files(self, file_paths: List[str]) -> Tuple[List[str], List[str]]:
        """分类文件为文本和图像"""
        text_files = []
        image_files = []

        for file_path in file_paths:
            if not os.path.exists(file_path):
                logger.warning(f"File not found: {file_path}")
                continue

            _, ext = os.path.splitext(file_path)
            ext_lower = ext.lower()

            if ext_lower in self.text_formats:
                text_files.append(file_path)
            elif ext_lower in self.image_formats:
                image_files.append(file_path)
            else:
                logger.warning(f"Unsupported file format: {file_path}")

        logger.info(f"Classified files: {len(text_files)} text, {len(image_files)} image")
        return text_files, image_files

    def classify_directory(self, source_dir: str) -> Tuple[List[str], List[str]]:
        """分类目录中的文件"""
        if not os.path.exists(source_dir):
            logger.error(f"Source directory not found: {source_dir}")
            return [], []

        all_files = []
        for root, _, files in os.walk(source_dir):
            for file in files:
                all_files.append(os.path.join(root, file))

        return self.classify_files(all_files)

    def process_text_files(self, text_files: List[str]) -> List[Dict[str, Any]]:
        """处理文本文件"""
        return self.text_preprocessor.process_directory(text_files)

    def process_image_files(self, image_files: List[str]) -> List[str]:
        """处理图像文件"""
        return self.image_preprocessor.process_images(image_files)

    def process_materials(self, material_paths: List[str]) -> Dict[str, List[str]]:
        """批量处理素材"""
        logger.info(f"Processing {len(material_paths)} materials")

        # 分类文件
        text_files, image_files = self.classify_files(material_paths)

        # 处理结果
        results = {
            "text_files": [],
            "image_files": []
        }

        # 处理文本文件
        if text_files:
            logger.info(f"Processing {len(text_files)} text files")
            results["text_files"] = self.process_text_files(text_files)

        # 处理图像文件
        if image_files:
            logger.info(f"Processing {len(image_files)} image files")
            results["image_files"] = self.process_image_files(image_files)

        logger.info(f"Processing completed: {len(results['text_files'])} text, {len(results['image_files'])} image")
        return results

    def process_directory(self, source_dir: str) -> Dict[str, List[str]]:
        """处理目录中的所有素材"""
        logger.info(f"Processing directory: {source_dir}")

        # 分类目录中的文件
        text_files, image_files = self.classify_directory(source_dir)

        # 处理结果
        results = {
            "text_files": [],
            "image_files": []
        }

        # 处理文本文件
        if text_files:
            results["text_files"] = self.process_text_files(text_files)

        # 处理图像文件
        if image_files:
            results["image_files"] = self.process_image_files(image_files)

        return results