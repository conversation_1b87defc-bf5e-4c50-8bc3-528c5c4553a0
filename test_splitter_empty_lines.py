#!/usr/bin/env python3
"""
测试切分器的空行切分功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from ai_workflow.preprocess.text_preprocessor.tools.splitter import Splitter


def test_empty_line_splitting():
    """测试超过两行空行的切分功能"""
    
    # 创建测试文本，包含超过两行的空行
    test_text = """第一段内容
这是第一段的第二行
这是第一段的第三行



第二段内容
这是第二段的第二行




第三段内容
这是第三段的第二行
这是第三段的第三行

第四段内容
只有一个空行，不应该被切分"""
    
    print("测试文本:")
    print(repr(test_text))
    print("\n" + "="*50 + "\n")
    
    # 创建切分器
    splitter = Splitter(segments_dir="test_segments", chunks_dir="test_chunks")
    
    # 测试按模式切分
    segments = splitter._split_by_patterns(test_text)
    
    print(f"切分结果: 共{len(segments)}个段落")
    print("\n")
    
    for i, segment in enumerate(segments, 1):
        print(f"段落 {i}: {segment['title']}")
        print(f"内容: {repr(segment['text'])}")
        print(f"字符数: {len(segment['text'])}")
        print("-" * 30)
    
    # 验证结果
    expected_segments = 3  # 应该有3个段落（第四段因为只有一个空行不会被切分）
    if len(segments) == expected_segments:
        print(f"✅ 测试通过: 正确识别了{expected_segments}个段落")
    else:
        print(f"❌ 测试失败: 期望{expected_segments}个段落，实际得到{len(segments)}个")
    
    return segments


def test_normal_chapter_splitting():
    """测试正常的章节切分功能（确保原功能不受影响）"""
    
    test_text = """前言内容
这是前言的内容

第一章 开始
这是第一章的内容
第一章的第二行

第二章 继续
这是第二章的内容"""
    
    print("\n" + "="*50)
    print("测试正常章节切分功能:")
    print("="*50 + "\n")
    
    splitter = Splitter(segments_dir="test_segments", chunks_dir="test_chunks")
    segments = splitter._split_by_patterns(test_text)
    
    print(f"切分结果: 共{len(segments)}个段落")
    print("\n")
    
    for i, segment in enumerate(segments, 1):
        print(f"段落 {i}: {segment['title']}")
        print(f"内容: {repr(segment['text'])}")
        print("-" * 30)
    
    # 验证结果
    expected_segments = 3  # 前言 + 第一章 + 第二章
    if len(segments) == expected_segments:
        print(f"✅ 章节切分测试通过: 正确识别了{expected_segments}个段落")
    else:
        print(f"❌ 章节切分测试失败: 期望{expected_segments}个段落，实际得到{len(segments)}个")


if __name__ == "__main__":
    print("开始测试切分器的空行切分功能...")
    
    # 测试空行切分
    test_empty_line_splitting()
    
    # 测试正常章节切分
    test_normal_chapter_splitting()
    
    print("\n测试完成!")
