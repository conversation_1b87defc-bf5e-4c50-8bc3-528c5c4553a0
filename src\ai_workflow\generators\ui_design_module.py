"""
UI设计模块
设计游戏界面元素和剧情分支交互
"""

import os
from typing import List, Dict

try:
    import renpy
    game_directory = renpy.config.gamedir
except:
    game_directory = os.getcwd()

class UIDesignModule:
    """UI设计模块"""
    
    def __init__(self):
        """初始化UI设计模块"""
        self.ui_dir = os.path.join(game_directory, "output", "ui")
        os.makedirs(self.ui_dir, exist_ok=True)
    
    def generate_ui_system(self, framework: Dict, narratives: List[Dict]) -> Dict:
        """
        生成游戏UI系统
        
        Args:
            framework: 游戏框架
            narratives: 节点叙事列表
            
        Returns:
            UI组件信息
        """
        print("生成UI系统...")
        
        ui_components = {
            "main_menu": self._generate_main_menu(),
            "game_menu": self._generate_game_menu(),
            "choice_menu": self._generate_choice_menu(),
            "save_load": self._generate_save_load_ui(),
            "preferences": self._generate_preferences_ui()
        }
        
        # 保存UI配置
        self._save_ui_config(ui_components)
        
        return ui_components
    
    def _generate_main_menu(self) -> Dict:
        """生成主菜单"""
        return {
            "type": "main_menu",
            "buttons": [
                {"text": "开始游戏", "action": "start"},
                {"text": "载入游戏", "action": "load"},
                {"text": "设置", "action": "preferences"},
                {"text": "退出", "action": "quit"}
            ]
        }
    
    def _generate_game_menu(self) -> Dict:
        """生成游戏内菜单"""
        return {
            "type": "game_menu",
            "buttons": [
                {"text": "历史", "action": "history"},
                {"text": "保存", "action": "save"},
                {"text": "载入", "action": "load"},
                {"text": "设置", "action": "preferences"},
                {"text": "返回标题", "action": "main_menu"}
            ]
        }
    
    def _generate_choice_menu(self) -> Dict:
        """生成选择菜单"""
        return {
            "type": "choice_menu",
            "style": "default",
            "layout": "vertical"
        }
    
    def _generate_save_load_ui(self) -> Dict:
        """生成存档/读档界面"""
        return {
            "type": "save_load",
            "slots": 10,
            "auto_save": True,
            "quick_save": True
        }
    
    def _generate_preferences_ui(self) -> Dict:
        """生成设置界面"""
        return {
            "type": "preferences",
            "options": [
                {"name": "文本速度", "type": "slider", "range": [0, 100]},
                {"name": "音乐音量", "type": "slider", "range": [0, 100]},
                {"name": "音效音量", "type": "slider", "range": [0, 100]},
                {"name": "全屏模式", "type": "checkbox"},
                {"name": "跳过已读文本", "type": "checkbox"}
            ]
        }
    
    def _save_ui_config(self, ui_components: Dict):
        """保存UI配置"""
        import json
        
        config_path = os.path.join(self.ui_dir, "ui_config.json")
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(ui_components, f, ensure_ascii=False, indent=2)
